<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Card UI Enhancement Test</title>
  <link rel="stylesheet" href="../styles/notely-theme.css">
  <link rel="stylesheet" href="../styles/improved-typography.css">
  <link rel="stylesheet" href="../styles/dramatic-typography.css">
  <style>
    body {
      background-color: #0F0F0F;
      color: #EAEAEA;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      padding: 2rem;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .comparison {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-bottom: 3rem;
    }
    
    .before, .after {
      padding: 1rem;
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.05);
    }
    
    h1, h2, h3 {
      color: #FFFFFF;
    }
    
    h1 {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    h2 {
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .changes {
      margin-top: 1rem;
      padding: 1rem;
      background-color: rgba(79, 70, 229, 0.1);
      border-left: 4px solid #4F46E5;
      border-radius: 4px;
    }
    
    .changes h3 {
      margin-top: 0;
      color: #A5B4FC;
    }
    
    .changes ul {
      margin-bottom: 0;
    }
    
    /* Before styles */
    .before .post-card {
      background-color: #171717;
      border: 1px solid #2A2A2A;
      border-radius: 8px;
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 16px;
      overflow: hidden;
    }
    
    .before .post-card .card-header {
      padding: 12px 16px;
      display: flex;
      align-items: center;
    }
    
    .before .post-card .card-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 12px;
      background-color: #2A2A2A;
    }
    
    .before .post-card .card-author {
      font-size: 14px;
      font-weight: 500;
      color: #CCCCCC;
      margin-bottom: 2px;
    }
    
    .before .post-card .card-timestamp {
      font-size: 12px;
      color: #888888;
    }
    
    .before .post-card .card-content {
      padding: 12px 16px;
    }
    
    .before .post-card .card-text {
      font-size: 14px;
      line-height: 1.2;
      color: #CCCCCC;
      margin-bottom: 12px;
    }
    
    .before .post-card .card-media {
      height: 160px;
      background-color: #2A2A2A;
      margin: 0 -16px;
    }
    
    .before .post-card .card-footer {
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid #2A2A2A;
    }
    
    .before .post-card .card-metrics {
      display: flex;
      gap: 12px;
    }
    
    .before .post-card .card-metric {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #888888;
      font-size: 12px;
    }
    
    .before .post-card .card-actions {
      display: flex;
      gap: 8px;
    }
    
    .before .post-card .card-action {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      background-color: #2A2A2A;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #888888;
    }
    
    /* After styles - with our enhancements */
    .after .post-card {
      background-color: #1D1D1D;
      border: 1px solid #2F2F2F;
      border-radius: 12px;
      box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
      margin-bottom: 24px;
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
    }
    
    .after .post-card:hover {
      transform: translateY(-8px);
      box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.3);
      border-color: #3F3F3F;
    }
    
    .after .post-card .card-header {
      padding: 18px 24px 14px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .after .post-card .card-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-right: 16px;
      background-color: #2A2A2A;
    }
    
    .after .post-card .card-author {
      font-size: 16px;
      font-weight: 600;
      color: #FFFFFF;
      margin-bottom: 4px;
    }
    
    .after .post-card .card-timestamp {
      font-size: 12px;
      color: #808080;
    }
    
    .after .post-card .card-content {
      padding: 18px 24px 16px;
    }
    
    .after .post-card .card-text {
      font-size: 15px;
      line-height: 1.5;
      color: #EAEAEA;
      margin-bottom: 16px;
    }
    
    .after .post-card .card-media {
      height: 180px;
      background-color: #2A2A2A;
      border-radius: 8px;
      overflow: hidden;
      margin-top: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    .after .post-card .card-footer {
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .after .post-card .card-metrics {
      display: flex;
      gap: 16px;
    }
    
    .after .post-card .card-metric {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #A0A0A0;
      font-size: 13px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.2s ease, color 0.2s ease;
    }
    
    .after .post-card .card-metric:hover {
      background-color: rgba(255, 255, 255, 0.05);
      color: #FFFFFF;
    }
    
    .after .post-card .card-actions {
      display: flex;
      gap: 12px;
    }
    
    .after .post-card .card-action {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: #2A2A2A;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #CCCCCC;
      transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
      position: relative;
    }
    
    .after .post-card .card-action:hover {
      background-color: #3A3A3A;
      color: #FFFFFF;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .after .post-card .card-action:focus-visible {
      outline: 2px solid #4F46E5;
      outline-offset: 2px;
    }
    
    .after .post-card .card-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 16px;
    }
    
    .after .post-card .card-tag {
      padding: 4px 12px;
      background-color: #2A2A2A;
      border-radius: 16px;
      font-size: 12px;
      color: #CCCCCC;
      transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
    }
    
    .after .post-card .card-tag:hover {
      background-color: #3A3A3A;
      color: #FFFFFF;
      transform: translateY(-2px);
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Card UI Enhancement Test</h1>
    
    <div class="comparison">
      <div class="before">
        <h2>Before</h2>
        <div class="post-card">
          <div class="card-header">
            <div class="card-avatar"></div>
            <div>
              <div class="card-author">John Doe</div>
              <div class="card-timestamp">2 days ago</div>
            </div>
          </div>
          <div class="card-content">
            <div class="card-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.</div>
            <div class="card-media"></div>
          </div>
          <div class="card-footer">
            <div class="card-metrics">
              <div class="card-metric">
                <span>❤️</span>
                <span>42</span>
              </div>
              <div class="card-metric">
                <span>💬</span>
                <span>12</span>
              </div>
              <div class="card-metric">
                <span>🔄</span>
                <span>5</span>
              </div>
            </div>
            <div class="card-actions">
              <div class="card-action">🗑️</div>
              <div class="card-action">🔗</div>
              <div class="card-action">📥</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="after">
        <h2>After</h2>
        <div class="post-card">
          <div class="card-header">
            <div class="card-avatar"></div>
            <div>
              <div class="card-author">John Doe</div>
              <div class="card-timestamp">2 days ago</div>
            </div>
          </div>
          <div class="card-content">
            <div class="card-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.</div>
            <div class="card-media"></div>
            <div class="card-tags">
              <div class="card-tag">design</div>
              <div class="card-tag">ui</div>
              <div class="card-tag">enhancement</div>
            </div>
          </div>
          <div class="card-footer">
            <div class="card-metrics">
              <div class="card-metric">
                <span>❤️</span>
                <span>42</span>
              </div>
              <div class="card-metric">
                <span>💬</span>
                <span>12</span>
              </div>
              <div class="card-metric">
                <span>🔄</span>
                <span>5</span>
              </div>
            </div>
            <div class="card-actions">
              <div class="card-action" title="Delete Post">🗑️</div>
              <div class="card-action" title="Copy Link">🔗</div>
              <div class="card-action" title="Download">📥</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="changes">
      <h3>Key Improvements</h3>
      <ul>
        <li><strong>Card Background & Boundaries:</strong> Increased contrast between card (#1D1D1D) and app background (#0F0F0F), improved border radius (12px), and enhanced shadow effects.</li>
        <li><strong>Spacing & Padding:</strong> Increased vertical padding (18px vs 12px) and horizontal padding (24px vs 16px) for better breathing room.</li>
        <li><strong>Typography & Hierarchy:</strong> Improved username styling (16px, 600 weight vs 14px, 500 weight), increased line height for content (1.5 vs 1.2), and brightened text color (#EAEAEA vs #CCCCCC).</li>
        <li><strong>Button & Icon Alignment:</strong> Increased button size (36px vs 32px), used circular buttons for better visual balance, added proper spacing between buttons (12px vs 8px).</li>
        <li><strong>Interactive Elements:</strong> Added hover effects for buttons and tags, implemented tooltips, and added focus states for accessibility.</li>
        <li><strong>Visual Separators:</strong> Added subtle dividers between card sections using rgba(255, 255, 255, 0.05) borders.</li>
        <li><strong>Responsive Design:</strong> Implemented responsive adjustments for different screen sizes.</li>
        <li><strong>Accessibility:</strong> Ensured proper color contrast, added focus states, and respected reduced motion preferences.</li>
      </ul>
    </div>
  </div>
</body>
</html>