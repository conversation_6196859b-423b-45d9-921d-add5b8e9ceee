import React, { useEffect } from 'react';
import '../styles/mobile-optimized-cards.css';

/**
 * Component that ensures mobile-optimized styles are applied to cards
 * This is a utility component that doesn't render anything visible
 */
const MobileOptimizedCard: React.FC = () => {
  useEffect(() => {
    // Apply mobile optimization to all cards
    const applyMobileOptimization = () => {
      // Add a class to the document body to indicate mobile optimization is active
      document.body.classList.add('mobile-optimized');
      
      // Force a repaint to ensure styles are applied
      document.body.style.display = 'none';
      // This line forces a reflow
      void document.body.offsetHeight;
      document.body.style.display = '';
    };

    // Apply optimization on mount
    applyMobileOptimization();

    // Apply optimization when window is resized
    window.addEventListener('resize', applyMobileOptimization);

    return () => {
      window.removeEventListener('resize', applyMobileOptimization);
    };
  }, []);

  return null; // This component doesn't render anything visible
};

export default MobileOptimizedCard;