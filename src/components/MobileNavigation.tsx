import React, { useState, useEffect } from 'react';
import '../styles/mobile-optimizations.css';

interface Platform {
  id: string;
  name: string;
  icon: string;
  count?: number;
}

interface MobileNavigationProps {
  activePlatform?: string;
  onPlatformChange?: (platform: string) => void;
}

/**
 * Enhanced mobile navigation component with touch-friendly controls
 */
const MobileNavigation: React.FC<MobileNavigationProps> = ({ 
  activePlatform = 'all',
  onPlatformChange = () => {}
}) => {
  const [showMobileNav, setShowMobileNav] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Sample platforms - in a real implementation, these would be passed as props
  const platforms: Platform[] = [
    { id: 'all', name: 'All', icon: 'grid' },
    { id: 'twitter', name: 'Twitter', icon: 'twitter' },
    { id: 'linkedin', name: 'LinkedIn', icon: 'linkedin' },
    { id: 'reddit', name: 'Reddit', icon: 'reddit' },
    { id: 'instagram', name: 'Instagram', icon: 'instagram' },
    { id: 'pinterest', name: 'Pinterest', icon: 'pinterest' },
    { id: 'web', name: 'Web', icon: 'globe' }
  ];

  // Check if we're on a mobile device
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // Only render the mobile navigation on mobile devices
  if (!isMobile) return null;

  return (
    <>
      {/* Mobile Navigation Toggle Button */}
      <button 
        className="mobile-nav-toggle"
        onClick={() => setShowMobileNav(!showMobileNav)}
        aria-label={showMobileNav ? "Close navigation menu" : "Open navigation menu"}
      >
        <span className="sr-only">{showMobileNav ? "Close Menu" : "Open Menu"}</span>
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        >
          {showMobileNav ? (
            <>
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </>
          ) : (
            <>
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </>
          )}
        </svg>
      </button>

      {/* Mobile Navigation Drawer */}
      <div className={`mobile-nav-drawer ${showMobileNav ? 'open' : ''}`}>
        <div className="mobile-nav-content">
          <div className="mobile-platform-tabs">
            {platforms.map(platform => (
              <button
                key={platform.id}
                className={`mobile-platform-tab ${activePlatform === platform.id ? 'active' : ''}`}
                onClick={() => {
                  onPlatformChange(platform.id);
                  setShowMobileNav(false);
                }}
              >
                <span className="mobile-platform-icon">
                  {/* This would be replaced with actual platform icons */}
                  {platform.icon === 'twitter' && (
                    <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231 5.45-6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
                    </svg>
                  )}
                  {platform.icon !== 'twitter' && (
                    <span>{platform.icon.charAt(0).toUpperCase()}</span>
                  )}
                </span>
                <span className="mobile-platform-name">{platform.name}</span>
                {platform.count && (
                  <span className="mobile-platform-count">{platform.count}</span>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileNavigation;