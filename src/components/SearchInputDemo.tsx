import React, { useState, useRef, useEffect } from 'react';
import '../styles/search-components.css';

/**
 * SearchInputDemo Component
 * Demonstrates the enhanced search input component with various states and variants
 */
const SearchInputDemo: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [demoResults, setDemoResults] = useState<string[]>([]);
  const searchContainerRef = useRef<HTMLDivElement>(null);
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    
    if (value.length > 0) {
      // Simulate loading state
      setIsLoading(true);
      setShowResults(false);
      
      // Simulate API delay
      setTimeout(() => {
        setIsLoading(false);
        setShowResults(true);
        
        // Generate demo results based on input
        const results = [
          `Result for "${value}" from Twitter`,
          `Result for "${value}" from LinkedIn`,
          `Result for "${value}" from Reddit`,
          `Another result containing "${value}"`,
        ];
        setDemoResults(results);
      }, 800);
    } else {
      setIsLoading(false);
      setShowResults(false);
    }
  };
  
  // Clear search input
  const clearSearch = () => {
    setSearchQuery('');
    setShowResults(false);
    setIsLoading(false);
  };
  
  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Highlight search terms in results
  const highlightSearchTerm = (text: string, term: string) => {
    if (!term.trim()) return text;
    
    const regex = new RegExp(`(${term})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? 
        <span key={index} className="search-result-highlight">{part}</span> : 
        <span key={index}>{part}</span>
    );
  };

  return (
    <div className="search-demo-container">
      <h2>Enhanced Search Component</h2>
      
      {/* Default Search */}
      <h3>Default Search</h3>
      <div className="search-container" ref={searchContainerRef}>
        <input
          type="search"
          className="search-input"
          placeholder="Search saved content..."
          value={searchQuery}
          onChange={handleSearchChange}
          aria-label="Search content"
        />
        <div className="search-icon">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        {searchQuery && (
          <button 
            className="search-clear" 
            onClick={clearSearch}
            aria-label="Clear search"
          >
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        )}
        <div className={`search-loading ${isLoading ? 'active' : ''}`}>
          <div className="search-loading-dots">
            <div className="search-loading-dot"></div>
            <div className="search-loading-dot"></div>
            <div className="search-loading-dot"></div>
          </div>
        </div>
        
        {/* Search Results Dropdown */}
        <div className={`search-results ${showResults ? 'active' : ''}`}>
          {demoResults.length > 0 ? (
            demoResults.map((result, index) => (
              <div 
                key={index} 
                className="search-result-item"
                tabIndex={0}
                role="option"
              >
                {highlightSearchTerm(result, searchQuery)}
              </div>
            ))
          ) : (
            <div className="search-no-results">
              No results found for "{searchQuery}"
            </div>
          )}
        </div>
      </div>
      
      {/* Compact Search */}
      <h3>Compact Search</h3>
      <div className="search-container compact">
        <input
          type="search"
          className="search-input"
          placeholder="Quick search..."
          aria-label="Quick search"
        />
        <div className="search-icon">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
      
      {/* Expanded Search */}
      <h3>Expanded Search (Header)</h3>
      <div className="search-container expanded">
        <input
          type="search"
          className="search-input"
          placeholder="Search across all your saved content..."
          aria-label="Expanded search"
        />
        <div className="search-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
      
      {/* Disabled Search */}
      <h3>Disabled State</h3>
      <div className="search-container">
        <input
          type="search"
          className="search-input"
          placeholder="Search is currently unavailable..."
          disabled
          aria-label="Disabled search"
        />
        <div className="search-icon">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default SearchInputDemo;