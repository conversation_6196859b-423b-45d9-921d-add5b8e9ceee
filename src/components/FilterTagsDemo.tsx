import React, { useEffect, useRef, useState } from 'react';
import '../styles/navigation-components.css';

/**
 * FilterTagsDemo Component
 * Demonstrates the enhanced filter and tag components
 */
const FilterTagsDemo: React.FC = () => {
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [activeTags, setActiveTags] = useState<string[]>([]);

  // Check for overflow in tabs container
  useEffect(() => {
    const checkOverflow = () => {
      if (tabsContainerRef.current) {
        const hasOverflow = tabsContainerRef.current.scrollWidth > tabsContainerRef.current.clientWidth;
        if (hasOverflow) {
          tabsContainerRef.current.classList.add('has-overflow');
        } else {
          tabsContainerRef.current.classList.remove('has-overflow');
        }
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    
    return () => {
      window.removeEventListener('resize', checkOverflow);
    };
  }, []);

  // Handle tab click
  const handleTabClick = (platform: string) => {
    setActiveTab(platform);
  };

  // Handle filter chip click
  const handleFilterClick = (filter: string) => {
    setActiveFilters(prev => 
      prev.includes(filter) 
        ? prev.filter(f => f !== filter) 
        : [...prev, filter]
    );
  };

  // Handle tag click
  const handleTagClick = (tag: string) => {
    setActiveTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag) 
        : [...prev, tag]
    );
  };

  // Clear all filters
  const clearAllFilters = () => {
    setActiveFilters([]);
  };

  return (
    <div className="filter-demo-container">
      <h2>Enhanced Navigation Components</h2>
      
      {/* Platform Tabs */}
      <h3>Platform Tabs</h3>
      <div className="platform-tabs-container" ref={tabsContainerRef}>
        <div className="platform-tabs">
          <div 
            className={`platform-tab all ${activeTab === 'all' ? 'active' : ''}`}
            onClick={() => handleTabClick('all')}
            tabIndex={0}
            role="tab"
            aria-selected={activeTab === 'all'}
          >
            <span className="platform-tab-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 6H20M4 12H20M4 18H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </span>
            All Platforms
            <span className="platform-tab-count">42</span>
          </div>
          
          <div 
            className={`platform-tab twitter ${activeTab === 'twitter' ? 'active' : ''}`}
            onClick={() => handleTabClick('twitter')}
            tabIndex={0}
            role="tab"
            aria-selected={activeTab === 'twitter'}
          >
            <span className="platform-tab-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 4.01C21 4.5 20.02 4.69 19 5C17.879 3.735 16.217 3.665 14.62 4.263C13.023 4.861 11.977 6.323 12 8.01V9.01C8.755 9.083 5.865 7.605 4 5.01C4 5.01 -0.182 12.733 8 16.01C6.128 17.247 4.261 18.088 2 18.01C5.308 19.687 8.913 20.322 12.034 19.503C15.614 18.567 18.556 15.906 19.685 11.952C20.0218 10.6867 20.189 9.3763 20.182 8.062C20.18 7.774 21.692 5.25 22 4.009V4.01Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </span>
            Twitter
            <span className="platform-tab-count">12</span>
          </div>
          
          <div 
            className={`platform-tab linkedin ${activeTab === 'linkedin' ? 'active' : ''}`}
            onClick={() => handleTabClick('linkedin')}
            tabIndex={0}
            role="tab"
            aria-selected={activeTab === 'linkedin'}
          >
            <span className="platform-tab-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 8C17.5913 8 19.1174 8.63214 20.2426 9.75736C21.3679 10.8826 22 12.4087 22 14V21H18V14C18 13.4696 17.7893 12.9609 17.4142 12.5858C17.0391 12.2107 16.5304 12 16 12C15.4696 12 14.9609 12.2107 14.5858 12.5858C14.2107 12.9609 14 13.4696 14 14V21H10V14C10 12.4087 10.6321 10.8826 11.7574 9.75736C12.8826 8.63214 14.4087 8 16 8Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M6 9H2V21H6V9Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M4 6C5.10457 6 6 5.10457 6 4C6 2.89543 5.10457 2 4 2C2.89543 2 2 2.89543 2 4C2 5.10457 2.89543 6 4 6Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </span>
            LinkedIn
            <span className="platform-tab-count">8</span>
          </div>
        </div>
      </div>
      
      {/* Filter Section */}
      <h3>Filter Components</h3>
      <div className="filter-section">
        <div className="filter-section-header">
          <div className="filter-section-title">Filter by</div>
          <div className="filter-section-actions">
            <button 
              className="filter-clear-all" 
              onClick={clearAllFilters}
              disabled={activeFilters.length === 0}
            >
              Clear all
            </button>
          </div>
        </div>
        
        <div className="filter-container">
          <div className="filter-group">
            <div className="filter-label">Date</div>
            <div 
              className={`filter-chip ${activeFilters.includes('today') ? 'active' : ''}`}
              onClick={() => handleFilterClick('today')}
              tabIndex={0}
              role="checkbox"
              aria-checked={activeFilters.includes('today')}
            >
              <span>Today</span>
            </div>
            <div 
              className={`filter-chip ${activeFilters.includes('this-week') ? 'active' : ''}`}
              onClick={() => handleFilterClick('this-week')}
              tabIndex={0}
              role="checkbox"
              aria-checked={activeFilters.includes('this-week')}
            >
              <span>This week</span>
            </div>
            <div 
              className={`filter-chip ${activeFilters.includes('this-month') ? 'active' : ''}`}
              onClick={() => handleFilterClick('this-month')}
              tabIndex={0}
              role="checkbox"
              aria-checked={activeFilters.includes('this-month')}
            >
              <span>This month</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Tags */}
      <h3>Tag Components</h3>
      <div className="tag-container">
        <div 
          className={`tag ${activeTags.includes('design') ? 'active' : ''}`}
          onClick={() => handleTagClick('design')}
          tabIndex={0}
        >
          Design
        </div>
        <div 
          className={`tag blue ${activeTags.includes('inspiration') ? 'active' : ''}`}
          onClick={() => handleTagClick('inspiration')}
          tabIndex={0}
        >
          Inspiration
        </div>
        <div 
          className={`tag green ${activeTags.includes('productivity') ? 'active' : ''}`}
          onClick={() => handleTagClick('productivity')}
          tabIndex={0}
        >
          Productivity
        </div>
        <div 
          className={`tag red ${activeTags.includes('important') ? 'active' : ''}`}
          onClick={() => handleTagClick('important')}
          tabIndex={0}
        >
          Important
        </div>
      </div>
    </div>
  );
};

export default FilterTagsDemo;