/* Modern Card Design System for Notely */

/* 
 * This file uses the design system tokens defined in design-system/tokens.css
 * and provides enhanced card styling for better readability and interaction
 */

/* Legacy variables for backward compatibility */
:root {
  /* Color System - mapped to design tokens */
  --notely-primary: var(--color-primary);
  --notely-primary-hover: var(--color-primary-dark);
  --notely-surface: var(--color-background);
  --notely-card-bg: var(--color-surface);
  --notely-border: var(--color-border);
  --notely-border-strong: var(--color-border-hover);
  --notely-text: var(--color-text-primary);
  --notely-text-muted: var(--color-text-secondary);
  --notely-sky: var(--color-info);
  --notely-mint: var(--color-success);
  --notely-coral: var(--color-error);
  
  /* Shadow System - mapped to design tokens */
  --shadow-sm: var(--shadow-sm);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  
  /* Spacing System - mapped to design tokens */
  --space-xs: var(--space-2);
  --space-sm: var(--space-3);
  --space-md: var(--space-4);
  --space-lg: var(--space-6);
  
  /* Typography - mapped to design tokens */
  --text-h1: var(--font-size-xl);
  --text-h2: var(--font-size-md);
  --text-body: var(--font-size-sm);
  --text-caption: var(--font-size-xs);
  --text-micro: 0.625rem; /* 10px */
}

/* Card Base */
.post-card {
  background-color: var(--card-background) !important;
  border: var(--border-width-1) solid var(--card-border-color) !important;
  border-radius: var(--card-border-radius) !important;
  box-shadow: var(--card-shadow) !important;
  transition: transform var(--duration-200) var(--ease-out),
              box-shadow var(--duration-200) var(--ease-out),
              border-color var(--duration-200) var(--ease-out) !important;
  overflow: hidden !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
}

.post-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--card-shadow-hover) !important;
  border-color: var(--color-border-hover) !important;
}

.post-card:focus-within {
  outline: none !important;
  box-shadow: var(--card-shadow-hover), 0 0 0 2px var(--focus-ring-color) !important;
}

/* Platform Indicator - Left Border */
.post-card-platform-indicator {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  width: 4px !important;
  background-color: var(--color-primary) !important;
}

/* Card Header */
.card-header {
  padding: var(--space-4) !important;
  border-bottom: var(--border-width-1) solid var(--color-border) !important;
  display: flex !important;
  align-items: flex-start !important;
  justify-content: space-between !important;
}

.card-header-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.card-header-avatar {
  width: 40px !important;
  height: 40px !important;
  border-radius: var(--radius-full) !important;
  margin-right: var(--space-3) !important;
  object-fit: cover !important;
  background-color: var(--color-surface-hover) !important;
}

.card-title {
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-text-primary) !important;
  margin-bottom: var(--space-1) !important;
  line-height: var(--line-height-tight) !important;
}

.card-subtitle {
  font-size: var(--font-size-xs) !important;
  color: var(--color-text-secondary) !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-2) !important;
  line-height: var(--line-height-normal) !important;
}

.card-timestamp {
  font-size: var(--font-size-xs) !important;
  color: var(--color-text-tertiary) !important;
}

/* Card Content */
.card-content {
  padding: var(--space-4) !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-3) !important;
}

.card-text {
  font-size: var(--font-size-sm) !important;
  color: var(--color-text-primary) !important;
  line-height: var(--line-height-relaxed) !important;
  overflow: hidden !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 3 !important;
  -webkit-box-orient: vertical !important;
  line-clamp: 3 !important; /* Standard property for compatibility */
}

/* Card Media */
.card-media-container {
  position: relative !important;
  width: 100% !important;
  margin-top: var(--space-3) !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
  background-color: var(--color-surface-hover) !important;
}

/* Default 16:9 aspect ratio container */
.card-media-container.aspect-16-9 {
  aspect-ratio: 16/9 !important;
}

/* Square aspect ratio container */
.card-media-container.aspect-1-1 {
  aspect-ratio: 1/1 !important;
}

/* Portrait aspect ratio container */
.card-media-container.aspect-4-5 {
  aspect-ratio: 4/5 !important;
}

/* Landscape aspect ratio container */
.card-media-container.aspect-3-2 {
  aspect-ratio: 3/2 !important;
}

.card-media {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  border-radius: var(--radius-lg) !important;
  transition: opacity var(--duration-300) var(--ease-out) !important;
}

/* Loading state */
.card-media-loading {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: var(--color-surface-hover) !important;
  border-radius: var(--radius-lg) !important;
}

.card-media-loading-spinner {
  width: 32px !important;
  height: 32px !important;
  border: 3px solid var(--color-border) !important;
  border-top-color: var(--color-primary) !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  to {
    transform: rotate(360deg) !important;
  }
}

/* Error state */
.card-media-error {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: var(--color-surface-hover) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-4) !important;
  text-align: center !important;
}

.card-media-error-icon {
  width: 32px !important;
  height: 32px !important;
  color: var(--color-error) !important;
  margin-bottom: var(--space-2) !important;
}

.card-media-error-text {
  font-size: var(--font-size-xs) !important;
  color: var(--color-text-secondary) !important;
}

/* Placeholder */
.card-media-placeholder {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: var(--color-surface-hover) !important;
  border-radius: var(--radius-lg) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.card-media-placeholder-icon {
  width: 48px !important;
  height: 48px !important;
  opacity: 0.3 !important;
  color: var(--color-text-tertiary) !important;
}

/* Multiple images indicator */
.card-media-count {
  position: absolute !important;
  top: var(--space-2) !important;
  right: var(--space-2) !important;
  background-color: rgba(0, 0, 0, 0.6) !important;
  color: white !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-1) var(--space-2) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-1) !important;
}

/* Lazy loading */
.card-media.lazy-load {
  opacity: 0 !important;
}

.card-media.lazy-load.loaded {
  opacity: 1 !important;
}

/* Responsive media handling */
@media (max-width: 768px) {
  .card-media-container {
    margin-top: var(--space-2) !important;
  }
  
  /* Adjust aspect ratio for smaller screens */
  .card-media-container.aspect-4-5 {
    aspect-ratio: 1/1 !important; /* Square on mobile */
  }
}

/* Blur-up loading technique */
.card-media-blur-up {
  filter: blur(10px) !important;
  transform: scale(1.05) !important;
  transition: filter var(--duration-300) var(--ease-out),
              transform var(--duration-300) var(--ease-out) !important;
}

.card-media-blur-up.loaded {
  filter: blur(0) !important;
  transform: scale(1) !important;
}

/* Image zoom on hover */
.card-media-zoom {
  transition: transform var(--duration-300) var(--ease-out) !important;
}

.card-media-container:hover .card-media-zoom {
  transform: scale(1.05) !important;
}

/* Card Footer */
.card-footer {
  padding: var(--space-4) !important;
  border-top: var(--border-width-1) solid var(--color-border) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  background-color: var(--color-surface) !important;
}

/* Metadata Row */
.metadata-row {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-4) !important;
  min-height: 24px !important;
}

.metadata-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-2) !important;
  color: var(--color-text-secondary) !important;
  font-size: var(--font-size-xs) !important;
  transition: all var(--duration-150) var(--ease-in-out) !important;
  padding: var(--space-1) !important;
  border-radius: var(--radius-md) !important;
}

.metadata-item:hover {
  background-color: var(--color-surface-hover) !important;
  color: var(--color-text-primary) !important;
}

.metadata-item:focus-visible {
  outline: 2px solid var(--focus-ring-color) !important;
  outline-offset: 2px !important;
}

.metadata-item.comments:hover {
  color: var(--color-info) !important;
}

.metadata-item.shares:hover {
  color: var(--color-success) !important;
}

.metadata-item.likes:hover {
  color: var(--color-error) !important;
}

/* Icon Container */
.icon-container {
  width: 18px !important;
  height: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

/* Action Buttons */
.action-buttons {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-2) !important;
}

.action-button {
  padding: var(--space-2) !important;
  border-radius: var(--radius-lg) !important;
  background-color: var(--color-surface) !important;
  border: var(--border-width-1) solid var(--color-border) !important;
  color: var(--color-text-secondary) !important;
  transition: all var(--duration-150) var(--ease-out) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  width: 32px !important;
  height: 32px !important;
}

.action-button:hover {
  background-color: var(--color-surface-hover) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-border-hover) !important;
}

.action-button:focus-visible {
  outline: 2px solid var(--focus-ring-color) !important;
  outline-offset: 2px !important;
}

.action-button:active {
  transform: scale(0.95) !important;
}

.action-button.delete:hover {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: var(--color-error) !important;
}

.action-button.link:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  color: var(--color-info) !important;
}

.action-button.download:hover {
  background-color: rgba(249, 115, 22, 0.1) !important;
  border-color: rgba(249, 115, 22, 0.3) !important;
  color: var(--color-warning) !important;
}

.action-button.copy:hover {
  background-color: rgba(16, 185, 129, 0.1) !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
  color: var(--color-success) !important;
}

/* Platform Badge */
.platform-badge {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-2) !important;
  padding: var(--space-1) var(--space-3) !important;
  border-radius: var(--radius-full) !important;
  background-color: var(--color-surface-hover) !important;
  border: var(--border-width-1) solid var(--color-border) !important;
  transition: all var(--duration-150) var(--ease-out) !important;
  cursor: pointer !important;
}

.platform-badge:hover {
  background-color: var(--color-background) !important;
  border-color: var(--color-border-hover) !important;
  transform: translateY(-1px) !important;
}

.platform-badge:focus-visible {
  outline: 2px solid var(--focus-ring-color) !important;
  outline-offset: 2px !important;
}

/* Platform-specific badges */
.platform-badge.twitter {
  background-color: rgba(29, 161, 242, 0.1) !important;
  border-color: rgba(29, 161, 242, 0.3) !important;
}

.platform-badge.twitter:hover {
  background-color: rgba(29, 161, 242, 0.15) !important;
  border-color: rgba(29, 161, 242, 0.4) !important;
}

.platform-badge.linkedin {
  background-color: rgba(0, 119, 181, 0.1) !important;
  border-color: rgba(0, 119, 181, 0.3) !important;
}

.platform-badge.linkedin:hover {
  background-color: rgba(0, 119, 181, 0.15) !important;
  border-color: rgba(0, 119, 181, 0.4) !important;
}

.platform-badge.reddit {
  background-color: rgba(255, 69, 0, 0.1) !important;
  border-color: rgba(255, 69, 0, 0.3) !important;
}

.platform-badge.reddit:hover {
  background-color: rgba(255, 69, 0, 0.15) !important;
  border-color: rgba(255, 69, 0, 0.4) !important;
}

.platform-badge.instagram {
  background-color: rgba(225, 48, 108, 0.1) !important;
  border-color: rgba(225, 48, 108, 0.3) !important;
}

.platform-badge.instagram:hover {
  background-color: rgba(225, 48, 108, 0.15) !important;
  border-color: rgba(225, 48, 108, 0.4) !important;
}

.platform-badge.pinterest {
  background-color: rgba(230, 0, 35, 0.1) !important;
  border-color: rgba(230, 0, 35, 0.3) !important;
}

.platform-badge.pinterest:hover {
  background-color: rgba(230, 0, 35, 0.15) !important;
  border-color: rgba(230, 0, 35, 0.4) !important;
}

.platform-badge.web {
  background-color: rgba(16, 185, 129, 0.1) !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
}

.platform-badge.web:hover {
  background-color: rgba(16, 185, 129, 0.15) !important;
  border-color: rgba(16, 185, 129, 0.4) !important;
}

.platform-icon {
  width: 16px !important;
  height: 16px !important;
  flex-shrink: 0 !important;
}

.platform-name {
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-text-secondary) !important;
  line-height: 1 !important;
}

/* Tag System */
.tag-container {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: var(--space-2) !important;
  margin-top: var(--space-3) !important;
}

.tag {
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-text-secondary) !important;
  background-color: var(--color-surface-hover) !important;
  border: var(--border-width-1) solid var(--color-border) !important;
  border-radius: var(--radius-full) !important;
  padding: var(--space-1) var(--space-2) !important;
  transition: all var(--duration-150) var(--ease-out) !important;
  cursor: pointer !important;
  display: inline-flex !important;
  align-items: center !important;
  line-height: 1 !important;
}

.tag:hover {
  background-color: rgba(99, 102, 241, 0.1) !important;
  border-color: rgba(99, 102, 241, 0.3) !important;
  color: var(--color-primary) !important;
  transform: translateY(-1px) !important;
}

.tag:focus-visible {
  outline: 2px solid var(--focus-ring-color) !important;
  outline-offset: 2px !important;
}

/* Platform Tabs */
.platform-tabs {
  display: flex !important;
  gap: var(--space-3) !important;
  margin-bottom: var(--space-6) !important;
  overflow-x: auto !important;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  padding-bottom: var(--space-2) !important; /* Space for scrollbar */
  position: relative !important;
}

.platform-tabs::-webkit-scrollbar {
  display: none !important;
}

.platform-tabs::after {
  content: '' !important;
  position: absolute !important;
  right: 0 !important;
  top: 0 !important;
  height: 100% !important;
  width: 40px !important;
  background: linear-gradient(to right, transparent, var(--color-background)) !important;
  pointer-events: none !important;
  opacity: 0.8 !important;
  display: none !important; /* Only show when overflowing */
}

.platform-tabs.overflow::after {
  display: block !important;
}

.platform-tab {
  padding: var(--space-2) var(--space-4) !important;
  border-radius: var(--radius-lg) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  background-color: var(--color-surface) !important;
  border: var(--border-width-1) solid var(--color-border) !important;
  color: var(--color-text-secondary) !important;
  transition: all var(--duration-150) var(--ease-out) !important;
  white-space: nowrap !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-2) !important;
}

.platform-tab:hover {
  background-color: var(--color-surface-hover) !important;
  border-color: var(--color-border-hover) !important;
  color: var(--color-text-primary) !important;
  transform: translateY(-1px) !important;
}

.platform-tab:focus-visible {
  outline: 2px solid var(--focus-ring-color) !important;
  outline-offset: 2px !important;
}

.platform-tab.active {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
  color: var(--color-text-on-primary) !important;
  box-shadow: var(--shadow-sm) !important;
}

.platform-tab-icon {
  width: 16px !important;
  height: 16px !important;
  flex-shrink: 0 !important;
}

/* Platform-specific tabs */
.platform-tab.twitter {
  border-color: var(--color-twitter) !important;
}

.platform-tab.twitter.active {
  background-color: var(--color-twitter) !important;
}

.platform-tab.linkedin {
  border-color: var(--color-linkedin) !important;
}

.platform-tab.linkedin.active {
  background-color: var(--color-linkedin) !important;
}

.platform-tab.reddit {
  border-color: var(--color-reddit) !important;
}

.platform-tab.reddit.active {
  background-color: var(--color-reddit) !important;
}

.platform-tab.instagram {
  border-color: var(--color-instagram) !important;
}

.platform-tab.instagram.active {
  background-color: var(--color-instagram) !important;
}

.platform-tab.pinterest {
  border-color: var(--color-pinterest) !important;
}

.platform-tab.pinterest.active {
  background-color: var(--color-pinterest) !important;
}

.platform-tab.web {
  border-color: var(--color-web) !important;
}

.platform-tab.web.active {
  background-color: var(--color-web) !important;
}

/* Search Input - Moved to search-components.css */

/* Grid Layout */
.posts-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)) !important;
  gap: var(--space-6) !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Storage Widget */
.storage-widget {
  background-color: var(--card-background) !important;
  border: var(--border-width-1) solid var(--card-border-color) !important;
  border-radius: var(--card-border-radius) !important;
  padding: var(--space-4) !important;
  margin-bottom: var(--space-6) !important;
  box-shadow: var(--card-shadow) !important;
  transition: box-shadow var(--duration-200) var(--ease-out) !important;
}

.storage-widget:hover {
  box-shadow: var(--card-shadow-hover) !important;
}

.storage-title {
  font-size: var(--font-size-md) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-text-primary) !important;
  margin-bottom: var(--space-3) !important;
}

.storage-progress {
  height: 8px !important;
  background-color: var(--color-surface-hover) !important;
  border-radius: var(--radius-full) !important;
  margin: var(--space-2) 0 !important;
  overflow: hidden !important;
  position: relative !important;
}

.storage-progress-bar {
  height: 100% !important;
  background-color: var(--color-primary) !important;
  border-radius: var(--radius-full) !important;
  transition: width var(--duration-300) var(--ease-out) !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
}

.storage-text {
  display: flex !important;
  justify-content: space-between !important;
  font-size: var(--font-size-xs) !important;
  color: var(--color-text-secondary) !important;
  margin-top: var(--space-2) !important;
}

.storage-used {
  font-weight: var(--font-weight-medium) !important;
}

.storage-total {
  color: var(--color-text-tertiary) !important;
}

/* Button Styles */
.btn {
  padding: var(--space-2) var(--space-4) !important;
  border-radius: var(--button-border-radius) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: all var(--duration-150) var(--ease-out) !important;
  cursor: pointer !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--space-2) !important;
}

.btn:focus-visible {
  outline: 2px solid var(--focus-ring-color) !important;
  outline-offset: 2px !important;
}

.btn-primary {
  background-color: var(--button-primary-background) !important;
  color: var(--button-primary-text) !important;
  border: none !important;
  box-shadow: var(--shadow-sm) !important;
}

.btn-primary:hover {
  background-color: var(--button-primary-background-hover) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-md) !important;
}

.btn-primary:active {
  transform: translateY(0) !important;
  box-shadow: var(--shadow-sm) !important;
}

.btn-secondary {
  background-color: var(--button-secondary-background) !important;
  color: var(--button-secondary-text) !important;
  border: var(--border-width-1) solid var(--button-secondary-border) !important;
}

.btn-secondary:hover {
  background-color: var(--color-surface-hover) !important;
  border-color: var(--color-border-hover) !important;
  transform: translateY(-1px) !important;
}

.btn-secondary:active {
  transform: translateY(0) !important;
}

.btn-icon {
  padding: var(--space-2) !important;
  border-radius: var(--radius-full) !important;
  width: 36px !important;
  height: 36px !important;
}

.btn-sm {
  padding: var(--space-1) var(--space-3) !important;
  font-size: var(--font-size-xs) !important;
}

.btn-lg {
  padding: var(--space-3) var(--space-6) !important;
  font-size: var(--font-size-md) !important;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
    gap: var(--space-4) !important;
  }
}

@media (max-width: 768px) {
  .posts-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-4) !important;
  }
  
  .platform-tabs {
    padding-bottom: var(--space-2) !important;
    gap: var(--space-2) !important;
  }
  
  .card-header {
    padding: var(--space-3) !important;
  }
  
  .card-content {
    padding: var(--space-3) !important;
  }
  
  .card-footer {
    padding: var(--space-3) !important;
  }
  
  .metadata-row {
    gap: var(--space-3) !important;
  }
  
  .action-buttons {
    gap: var(--space-1) !important;
  }
}

@media (max-width: 480px) {
  .card-header-avatar {
    width: 32px !important;
    height: 32px !important;
  }
  
  .card-title {
    font-size: var(--font-size-md) !important;
  }
  
  .platform-tab {
    padding: var(--space-2) var(--space-3) !important;
  }
  
  .metadata-item {
    font-size: 0.7rem !important;
  }
  
  .action-button {
    width: 28px !important;
    height: 28px !important;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .post-card,
  .platform-badge,
  .tag,
  .platform-tab,
  .action-button,
  .btn,
  .storage-progress-bar {
    transition: none !important;
  }
  
  .post-card:hover {
    transform: none !important;
  }
  
  .platform-badge:hover,
  .tag:hover,
  .btn:hover {
    transform: none !important;
  }
}

/* High Contrast Mode Adjustments */
@media (forced-colors: active) {
  .post-card {
    border: 2px solid CanvasText !important;
  }
  
  .card-header,
  .card-footer {
    border-color: CanvasText !important;
  }
  
  .platform-badge,
  .tag,
  .action-button,
  .btn-secondary {
    border: 1px solid CanvasText !important;
  }
  
  .btn-primary {
    border: 1px solid ButtonText !important;
  }
  
  .storage-progress {
    border: 1px solid CanvasText !important;
  }
}/* Pla
tform Indicators - Enhanced System */

/* Platform indicator base styles */
.platform-indicator {
  position: absolute !important;
  z-index: 1 !important;
  transition: all var(--duration-200) var(--ease-out) !important;
}

/* Left border indicator */
.platform-indicator-border {
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  width: 4px !important;
}

/* Corner badge indicator */
.platform-indicator-corner {
  top: 0 !important;
  left: 0 !important;
  width: 0 !important;
  height: 0 !important;
  border-style: solid !important;
  border-width: 24px 24px 0 0 !important;
  border-color: transparent !important;
}

/* Ribbon indicator */
.platform-indicator-ribbon {
  top: var(--space-4) !important;
  left: -var(--space-3) !important;
  padding: var(--space-1) var(--space-3) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  color: white !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Top border indicator */
.platform-indicator-top {
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
}

/* Platform-specific colors */

/* Twitter/X */
.platform-indicator.twitter.platform-indicator-border {
  background-color: var(--color-twitter) !important;
}

.platform-indicator.twitter.platform-indicator-corner {
  border-top-color: var(--color-twitter) !important;
}

.platform-indicator.twitter.platform-indicator-ribbon {
  background-color: var(--color-twitter) !important;
}

.platform-indicator.twitter.platform-indicator-top {
  background-color: var(--color-twitter) !important;
}

/* LinkedIn */
.platform-indicator.linkedin.platform-indicator-border {
  background-color: var(--color-linkedin) !important;
}

.platform-indicator.linkedin.platform-indicator-corner {
  border-top-color: var(--color-linkedin) !important;
}

.platform-indicator.linkedin.platform-indicator-ribbon {
  background-color: var(--color-linkedin) !important;
}

.platform-indicator.linkedin.platform-indicator-top {
  background-color: var(--color-linkedin) !important;
}

/* Reddit */
.platform-indicator.reddit.platform-indicator-border {
  background-color: var(--color-reddit) !important;
}

.platform-indicator.reddit.platform-indicator-corner {
  border-top-color: var(--color-reddit) !important;
}

.platform-indicator.reddit.platform-indicator-ribbon {
  background-color: var(--color-reddit) !important;
}

.platform-indicator.reddit.platform-indicator-top {
  background-color: var(--color-reddit) !important;
}

/* Instagram */
.platform-indicator.instagram.platform-indicator-border {
  background-color: var(--color-instagram) !important;
}

.platform-indicator.instagram.platform-indicator-corner {
  border-top-color: var(--color-instagram) !important;
}

.platform-indicator.instagram.platform-indicator-ribbon {
  background-color: var(--color-instagram) !important;
}

.platform-indicator.instagram.platform-indicator-top {
  background-color: var(--color-instagram) !important;
}

/* Pinterest */
.platform-indicator.pinterest.platform-indicator-border {
  background-color: var(--color-pinterest) !important;
}

.platform-indicator.pinterest.platform-indicator-corner {
  border-top-color: var(--color-pinterest) !important;
}

.platform-indicator.pinterest.platform-indicator-ribbon {
  background-color: var(--color-pinterest) !important;
}

.platform-indicator.pinterest.platform-indicator-top {
  background-color: var(--color-pinterest) !important;
}

/* Web */
.platform-indicator.web.platform-indicator-border {
  background-color: var(--color-web) !important;
}

.platform-indicator.web.platform-indicator-corner {
  border-top-color: var(--color-web) !important;
}

.platform-indicator.web.platform-indicator-ribbon {
  background-color: var(--color-web) !important;
}

.platform-indicator.web.platform-indicator-top {
  background-color: var(--color-web) !important;
}

/* Platform icon in header */
.platform-icon-header {
  width: 16px !important;
  height: 16px !important;
  margin-right: var(--space-2) !important;
  opacity: 0.8 !important;
  flex-shrink: 0 !important;
}

/* Platform color dot */
.platform-dot {
  width: 8px !important;
  height: 8px !important;
  border-radius: var(--radius-full) !important;
  margin-right: var(--space-2) !important;
  flex-shrink: 0 !important;
}

.platform-dot.twitter {
  background-color: var(--color-twitter) !important;
}

.platform-dot.linkedin {
  background-color: var(--color-linkedin) !important;
}

.platform-dot.reddit {
  background-color: var(--color-reddit) !important;
}

.platform-dot.instagram {
  background-color: var(--color-instagram) !important;
}

.platform-dot.pinterest {
  background-color: var(--color-pinterest) !important;
}

.platform-dot.web {
  background-color: var(--color-web) !important;
}

/* Platform mini badge */
.platform-mini-badge {
  display: inline-flex !important;
  align-items: center !important;
  padding: var(--space-1) var(--space-2) !important;
  border-radius: var(--radius-full) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  color: white !important;
  margin-right: var(--space-2) !important;
  flex-shrink: 0 !important;
}

.platform-mini-badge.twitter {
  background-color: var(--color-twitter) !important;
}

.platform-mini-badge.linkedin {
  background-color: var(--color-linkedin) !important;
}

.platform-mini-badge.reddit {
  background-color: var(--color-reddit) !important;
}

.platform-mini-badge.instagram {
  background-color: var(--color-instagram) !important;
}

.platform-mini-badge.pinterest {
  background-color: var(--color-pinterest) !important;
}

.platform-mini-badge.web {
  background-color: var(--color-web) !important;
}

/* Platform indicator with hover effect */
.post-card:hover .platform-indicator-border {
  width: 6px !important; /* Slightly wider on hover */
}

.post-card:hover .platform-indicator-top {
  height: 6px !important; /* Slightly taller on hover */
}

.post-card:hover .platform-indicator-corner {
  border-width: 28px 28px 0 0 !important; /* Slightly larger on hover */
}

/* Platform indicator with icon */
.platform-indicator-icon {
  position: absolute !important;
  top: var(--space-3) !important;
  right: var(--space-3) !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: var(--radius-full) !important;
  background-color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: var(--shadow-sm) !important;
}

.platform-indicator-icon img,
.platform-indicator-icon svg {
  width: 16px !important;
  height: 16px !important;
}

/* Platform-specific icon backgrounds */
.platform-indicator-icon.twitter {
  background-color: var(--color-twitter) !important;
}

.platform-indicator-icon.linkedin {
  background-color: var(--color-linkedin) !important;
}

.platform-indicator-icon.reddit {
  background-color: var(--color-reddit) !important;
}

.platform-indicator-icon.instagram {
  background-color: var(--color-instagram) !important;
}

.platform-indicator-icon.pinterest {
  background-color: var(--color-pinterest) !important;
}

.platform-indicator-icon.web {
  background-color: var(--color-web) !important;
}

/* Accessibility improvements for platform indicators */
@media (prefers-reduced-motion: reduce) {
  .platform-indicator,
  .post-card:hover .platform-indicator-border,
  .post-card:hover .platform-indicator-top,
  .post-card:hover .platform-indicator-corner {
    transition: none !important;
  }
}

/* High contrast mode adjustments */
@media (forced-colors: active) {
  .platform-indicator-border,
  .platform-indicator-top,
  .platform-indicator-corner,
  .platform-indicator-ribbon,
  .platform-dot,
  .platform-mini-badge,
  .platform-indicator-icon {
    border: 1px solid CanvasText !important;
  }
}