/* 
 * Button System for Notely
 * A comprehensive set of button styles with proper visual hierarchy and interaction states
 */

/* ===== BUTTON BASE STYLES ===== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  user-select: none;
}

/* Button sizes */
.btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
  border-radius: 0.375rem;
}

.btn-md {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 0.5rem;
}

.btn-lg {
  padding: 0.625rem 1.25rem;
  font-size: 1rem;
  border-radius: 0.5rem;
}

.btn-xl {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
  border-radius: 0.625rem;
}

/* Button variants */

/* Primary button */
.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:active {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(0);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Secondary button */
.btn-secondary {
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-secondary:active {
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(0);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Outline button */
.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-outline:hover {
  background-color: rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-outline:active {
  background-color: rgba(79, 70, 229, 0.15);
  transform: translateY(0);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Ghost button */
.btn-ghost {
  background-color: transparent;
  color: var(--color-text-primary);
  border-color: transparent;
}

.btn-ghost:hover {
  background-color: var(--color-surface-hover);
  transform: translateY(-1px);
}

.btn-ghost:active {
  background-color: var(--color-surface-hover);
  transform: translateY(0);
}

/* Link button */
.btn-link {
  background-color: transparent;
  color: var(--color-primary);
  border-color: transparent;
  padding: 0;
  text-decoration: none;
}

.btn-link:hover {
  text-decoration: underline;
  transform: none;
  box-shadow: none;
}

.btn-link:active {
  color: var(--color-primary-dark);
  transform: none;
  box-shadow: none;
}

/* Icon button */
.btn-icon {
  padding: 0.5rem;
  border-radius: 0.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon.btn-xs {
  padding: 0.25rem;
  border-radius: 0.375rem;
}

.btn-icon.btn-sm {
  padding: 0.375rem;
  border-radius: 0.375rem;
}

.btn-icon.btn-md {
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.btn-icon.btn-lg {
  padding: 0.625rem;
  border-radius: 0.5rem;
}

.btn-icon.btn-xl {
  padding: 0.75rem;
  border-radius: 0.625rem;
}

.btn-icon.btn-round {
  border-radius: 9999px;
}

/* Button states */

/* Disabled state */
.btn:disabled,
.btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Loading state */
.btn.loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.btn.loading::after {
  content: '';
  position: absolute;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 2px solid currentColor;
  border-right-color: transparent;
  animation: spin 0.75s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Focus state */
.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.4);
}

/* Button with icon */
.btn-icon-left {
  display: inline-flex;
  align-items: center;
}

.btn-icon-left svg,
.btn-icon-left img {
  margin-right: 0.5rem;
}

.btn-icon-right {
  display: inline-flex;
  align-items: center;
}

.btn-icon-right svg,
.btn-icon-right img {
  margin-left: 0.5rem;
}

/* Button group */
.btn-group {
  display: inline-flex;
  border-radius: 0.5rem;
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.btn-group .btn:last-child {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  border-right-width: 1px;
}

/* Button with badge */
.btn-with-badge {
  position: relative;
}

.btn-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1.25rem;
  height: 1.25rem;
  padding: 0 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
  color: white;
  background-color: var(--color-error);
  border-radius: 9999px;
  border: 2px solid var(--color-background);
}

/* Button with tooltip */
.btn-tooltip {
  position: relative;
}

.btn-tooltip-text {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 0.5rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  color: white;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 0.25rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;
  z-index: 10;
}

.btn-tooltip-text::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0.25rem;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
}

.btn-tooltip:hover .btn-tooltip-text {
  opacity: 1;
  visibility: visible;
}

/* Button with ripple effect */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple .ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Button color variants */

/* Success button */
.btn-success {
  background-color: var(--color-success);
  color: white;
  border-color: var(--color-success);
}

.btn-success:hover {
  background-color: var(--color-success-dark, #0e9f6e);
  border-color: var(--color-success-dark, #0e9f6e);
}

/* Warning button */
.btn-warning {
  background-color: var(--color-warning);
  color: white;
  border-color: var(--color-warning);
}

.btn-warning:hover {
  background-color: var(--color-warning-dark, #d97706);
  border-color: var(--color-warning-dark, #d97706);
}

/* Error button */
.btn-error {
  background-color: var(--color-error);
  color: white;
  border-color: var(--color-error);
}

.btn-error:hover {
  background-color: var(--color-error-dark, #dc2626);
  border-color: var(--color-error-dark, #dc2626);
}

/* Info button */
.btn-info {
  background-color: var(--color-info);
  color: white;
  border-color: var(--color-info);
}

.btn-info:hover {
  background-color: var(--color-info-dark, #2563eb);
  border-color: var(--color-info-dark, #2563eb);
}

/* Platform-specific buttons */

.btn-twitter {
  background-color: var(--color-twitter);
  color: white;
  border-color: var(--color-twitter);
}

.btn-twitter:hover {
  background-color: #0d8bd9;
  border-color: #0d8bd9;
}

.btn-linkedin {
  background-color: var(--color-linkedin);
  color: white;
  border-color: var(--color-linkedin);
}

.btn-linkedin:hover {
  background-color: #00669c;
  border-color: #00669c;
}

.btn-reddit {
  background-color: var(--color-reddit);
  color: white;
  border-color: var(--color-reddit);
}

.btn-reddit:hover {
  background-color: #e03c00;
  border-color: #e03c00;
}

.btn-instagram {
  background-color: var(--color-instagram);
  color: white;
  border-color: var(--color-instagram);
  background-image: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.btn-instagram:hover {
  opacity: 0.9;
}

.btn-pinterest {
  background-color: var(--color-pinterest);
  color: white;
  border-color: var(--color-pinterest);
}

.btn-pinterest:hover {
  background-color: #cc001f;
  border-color: #cc001f;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
  
  .btn:hover,
  .btn:active {
    transform: none;
  }
  
  .btn.loading::after {
    animation: none;
  }
  
  .btn-ripple .ripple {
    display: none;
  }
}

/* High contrast mode */
@media (forced-colors: active) {
  .btn {
    border: 1px solid ButtonText;
    color: ButtonText;
    background-color: ButtonFace;
  }
  
  .btn:hover,
  .btn:focus {
    border-color: Highlight;
    color: Highlight;
  }
  
  .btn-primary,
  .btn-success,
  .btn-warning,
  .btn-error,
  .btn-info,
  .btn-twitter,
  .btn-linkedin,
  .btn-reddit,
  .btn-instagram,
  .btn-pinterest {
    border: 1px solid ButtonText;
    color: ButtonText;
    background-color: ButtonFace;
  }
  
  .btn-primary:hover,
  .btn-success:hover,
  .btn-warning:hover,
  .btn-error:hover,
  .btn-info:hover,
  .btn-twitter:hover,
  .btn-linkedin:hover,
  .btn-reddit:hover,
  .btn-instagram:hover,
  .btn-pinterest:hover {
    border-color: Highlight;
    color: Highlight;
  }
}

/* Mobile optimizations */
@media (max-width: 767px) {
  .btn {
    min-height: 44px; /* Minimum touch target size */
  }
  
  .btn-icon {
    min-width: 44px;
    min-height: 44px;
  }
}