/* 
 * Mobile-Optimized Card Styles for Notely
 * Ensures proper styling on smaller screens and fixes style conflicts
 */

/* Card Base - Force important styles for all cards */
.post-card,
article.post-card,
.notely-card,
article.notely-card,
.post-card.notely-card,
article.post-card.notely-card {
  border-radius: 1.25rem !important;
  box-shadow: rgba(0, 0, 0, 0.4) 0px 15px 35px, rgba(255, 255, 255, 0.08) 0px 0px 0px 1px !important;
  background: linear-gradient(135deg, rgb(30, 30, 30) 0%, rgb(24, 24, 24) 50%, rgb(22, 22, 22) 100%) !important;
  border: 1px solid #2F2F2F !important;
  border-top-width: 6px !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  overflow: hidden !important;
}

/* Card hover state */
.post-card:hover,
article.post-card:hover,
.notely-card:hover,
article.notely-card:hover,
.post-card.notely-card:hover,
article.post-card.notely-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: rgba(0, 0, 0, 0.4) 0px 20px 40px, rgba(255, 255, 255, 0.1) 0px 0px 0px 1px !important;
}

/* Platform-specific top borders */
.post-card[data-post-id*="twitter"],
.post-card div[title="X/Twitter"],
article.post-card[data-post-id*="twitter"],
article.post-card div[title="X/Twitter"] {
  border-top-color: #1da1f2 !important;
}

/* Card Header Improvements */
.post-card .notely-breathing-compact-md,
article.post-card .notely-breathing-compact-md {
  padding: 1rem !important;
}

@media (min-width: 640px) {
  .post-card .notely-breathing-compact-md,
  article.post-card .notely-breathing-compact-md {
    padding: 1rem 1.5rem !important;
  }
}

/* Avatar Improvements */
.post-card .w-8.h-8,
.post-card .sm\\:w-10.sm\\:h-10,
.post-card .rounded-full.bg-gray-200,
article.post-card .w-8.h-8,
article.post-card .sm\\:w-10.sm\\:h-10,
article.post-card .rounded-full.bg-gray-200 {
  width: 40px !important;
  height: 40px !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

@media (min-width: 640px) {
  .post-card .w-8.h-8,
  .post-card .sm\\:w-10.sm\\:h-10,
  .post-card .rounded-full.bg-gray-200,
  article.post-card .w-8.h-8,
  article.post-card .sm\\:w-10.sm\\:h-10,
  article.post-card .rounded-full.bg-gray-200 {
    width: 48px !important;
    height: 48px !important;
  }
}

/* Author Name Improvements */
.post-card .author-name,
.post-card .font-bold.text-lg,
article.post-card .author-name,
article.post-card .font-bold.text-lg {
  font-size: 1.125rem !important;
  font-weight: 700 !important;
  letter-spacing: -0.01em !important;
  color: rgba(255, 255, 255, 0.95) !important;
}

@media (min-width: 640px) {
  .post-card .author-name,
  .post-card .font-bold.text-lg,
  article.post-card .author-name,
  article.post-card .font-bold.text-lg {
    font-size: 1.25rem !important;
  }
}

/* Author Handle & Timestamp Improvements */
.post-card .author-handle,
.post-card .text-notely-text-muted.notely-body,
article.post-card .author-handle,
article.post-card .text-notely-text-muted.notely-body {
  font-size: 0.75rem !important;
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 500 !important;
}

@media (min-width: 640px) {
  .post-card .author-handle,
  .post-card .text-notely-text-muted.notely-body,
  article.post-card .author-handle,
  article.post-card .text-notely-text-muted.notely-body {
    font-size: 0.875rem !important;
  }
}

.post-card .timestamp,
.post-card .text-notely-text-muted.hover\\:text-notely-text-secondary,
article.post-card .timestamp,
article.post-card .text-notely-text-muted.hover\\:text-notely-text-secondary {
  font-size: 0.75rem !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

@media (min-width: 640px) {
  .post-card .timestamp,
  .post-card .text-notely-text-muted.hover\\:text-notely-text-secondary,
  article.post-card .timestamp,
  article.post-card .text-notely-text-muted.hover\\:text-notely-text-secondary {
    font-size: 0.875rem !important;
  }
}

/* Post Content Improvements */
.post-card .post-content,
.post-card .font-normal.text-[15px],
article.post-card .post-content,
article.post-card .font-normal.text-[15px] {
  padding: 0.75rem 1rem !important;
  font-size: 0.9375rem !important;
  line-height: 1.6 !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

@media (min-width: 640px) {
  .post-card .post-content,
  .post-card .font-normal.text-[15px],
  article.post-card .post-content,
  article.post-card .font-normal.text-[15px] {
    padding: 1rem 1.5rem !important;
    font-size: 1.0625rem !important;
  }
}

/* Card Footer Improvements */
.post-card .mt-3.pt-3.pb-2,
.post-card .notely-post-footer > div,
article.post-card .mt-3.pt-3.pb-2,
article.post-card .notely-post-footer > div {
  padding: 0.75rem 1rem !important;
  background: linear-gradient(to bottom, rgba(30, 30, 30, 0.5), rgba(24, 24, 24, 0.8)) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

@media (min-width: 640px) {
  .post-card .mt-3.pt-3.pb-2,
  .post-card .notely-post-footer > div,
  article.post-card .mt-3.pt-3.pb-2,
  article.post-card .notely-post-footer > div {
    padding: 1rem 1.5rem !important;
  }
}

/* Interaction Buttons Improvements */
.post-card .group.p-2,
.post-card button.group,
article.post-card .group.p-2,
article.post-card button.group {
  padding: 0.5rem !important;
  border-radius: 0.5rem !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  transition: all 0.2s ease !important;
}

.post-card .group.p-2:hover,
.post-card button.group:hover,
article.post-card .group.p-2:hover,
article.post-card button.group:hover {
  transform: translateY(-2px) scale(1.05) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Metadata Items Improvements */
.post-card .flex.items-center.space-x-4 > div,
.post-card .group.flex.items-center,
article.post-card .flex.items-center.space-x-4 > div,
article.post-card .group.flex.items-center {
  padding: 0.25rem 0.5rem !important;
  border-radius: 0.375rem !important;
  background-color: rgba(255, 255, 255, 0.03) !important;
  transition: all 0.2s ease !important;
}

@media (min-width: 640px) {
  .post-card .flex.items-center.space-x-4 > div,
  .post-card .group.flex.items-center,
  article.post-card .flex.items-center.space-x-4 > div,
  article.post-card .group.flex.items-center {
    padding: 0.375rem 0.75rem !important;
  }
}

.post-card .flex.items-center.space-x-4 > div:hover,
.post-card .group.flex.items-center:hover,
article.post-card .flex.items-center.space-x-4 > div:hover,
article.post-card .group.flex.items-center:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Platform Badge Improvements */
.post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full,
.post-card div[title="X/Twitter"],
article.post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full,
article.post-card div[title="X/Twitter"] {
  padding: 0.25rem 0.625rem !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  transition: all 0.2s ease !important;
  border-radius: 9999px !important;
}

@media (min-width: 640px) {
  .post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full,
  .post-card div[title="X/Twitter"],
  article.post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full,
  article.post-card div[title="X/Twitter"] {
    padding: 0.375rem 0.875rem !important;
  }
}

.post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full:hover,
.post-card div[title="X/Twitter"]:hover,
article.post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full:hover,
article.post-card div[title="X/Twitter"]:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Touch-friendly targets for mobile */
@media (max-width: 640px) {
  /* Increase tap target sizes */
  .post-card .group.p-2,
  .post-card button.group,
  article.post-card .group.p-2,
  article.post-card button.group {
    min-width: 44px !important;
    min-height: 44px !important;
  }
  
  /* Add more spacing between interactive elements */
  .post-card .flex.items-center.space-x-4,
  article.post-card .flex.items-center.space-x-4 {
    gap: 12px !important;
  }
  
  .post-card .flex.items-center.space-x-2,
  article.post-card .flex.items-center.space-x-2 {
    gap: 8px !important;
  }
  
  /* Ensure text is readable on small screens */
  .post-card .text-sm,
  article.post-card .text-sm {
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
  }
}

/* Accessibility improvements for touch devices */
@media (hover: none) {
  /* Provide visual feedback on touch */
  .post-card .group.p-2:active,
  .post-card button.group:active,
  article.post-card .group.p-2:active,
  article.post-card button.group:active {
    background-color: rgba(255, 255, 255, 0.15) !important;
    transform: scale(0.98) !important;
  }
  
  /* Disable hover effects that might cause confusion */
  .post-card:hover,
  article.post-card:hover {
    transform: none !important;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .post-card,
  .post-card:hover,
  .post-card .group.p-2,
  .post-card button.group,
  article.post-card,
  article.post-card:hover,
  article.post-card .group.p-2,
  article.post-card button.group {
    transition: none !important;
    transform: none !important;
  }
}