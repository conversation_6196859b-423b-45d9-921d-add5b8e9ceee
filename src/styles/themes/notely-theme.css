/* 
 * Notely Theme - Optimized
 * Consolidated theme-specific styles and overrides
 */

/* ===== NOTELY BRAND COLORS ===== */
:root {
  /* Notely brand colors */
  --notely-primary: #4f46e5;
  --notely-primary-light: #6366f1;
  --notely-primary-dark: #4338ca;
  --notely-accent: #10b981;
  --notely-accent-light: #34d399;
  --notely-accent-dark: #059669;
  
  /* Notely semantic colors */
  --notely-success: var(--color-success);
  --notely-warning: var(--color-warning);
  --notely-error: var(--color-error);
  --notely-info: var(--color-info);
  
  /* Notely surface colors */
  --notely-bg: var(--color-background);
  --notely-surface: var(--color-surface);
  --notely-surface-hover: var(--color-surface-hover);
  --notely-border: var(--color-border);
  --notely-border-hover: var(--color-border-hover);
  
  /* Notely text colors */
  --notely-text-primary: var(--color-text-primary);
  --notely-text-secondary: var(--color-text-secondary);
  --notely-text-tertiary: var(--color-text-tertiary);
  
  /* Notely shadows */
  --notely-shadow-sm: var(--shadow-sm);
  --notely-shadow-md: var(--shadow-md);
  --notely-shadow-lg: var(--shadow-lg);
  --notely-shadow-xl: var(--shadow-xl);
  
  /* Notely spacing */
  --notely-spacing-xs: var(--space-1);
  --notely-spacing-sm: var(--space-2);
  --notely-spacing-md: var(--space-4);
  --notely-spacing-lg: var(--space-6);
  --notely-spacing-xl: var(--space-8);
  
  /* Notely border radius */
  --notely-radius-sm: var(--radius-md);
  --notely-radius-md: var(--radius-lg);
  --notely-radius-lg: var(--radius-xl);
  --notely-radius-xl: var(--radius-2xl);
  --notely-radius-full: var(--radius-full);
}

/* ===== NOTELY COMPONENT STYLES ===== */

/* Notely buttons */
.notely-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--notely-spacing-sm);
  padding: var(--notely-spacing-sm) var(--notely-spacing-md);
  border-radius: var(--notely-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-none);
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  border: var(--border-width-1) solid transparent;
  user-select: none;
}

.notely-btn-primary {
  background-color: var(--notely-primary);
  color: white;
  border-color: var(--notely-primary);
}

.notely-btn-primary:hover {
  background-color: var(--notely-primary-dark);
  border-color: var(--notely-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-md);
}

.notely-btn-secondary {
  background-color: var(--notely-surface);
  color: var(--notely-text-primary);
  border-color: var(--notely-border);
}

.notely-btn-secondary:hover {
  background-color: var(--notely-surface-hover);
  border-color: var(--notely-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-md);
}

.notely-btn-accent {
  background-color: var(--notely-accent);
  color: white;
  border-color: var(--notely-accent);
}

.notely-btn-accent:hover {
  background-color: var(--notely-accent-dark);
  border-color: var(--notely-accent-dark);
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-md);
}

/* Notely cards */
.notely-card {
  background-color: var(--notely-surface);
  border: var(--border-width-1) solid var(--notely-border);
  border-radius: var(--notely-radius-lg);
  box-shadow: var(--notely-shadow-md);
  overflow: hidden;
  transition: transform var(--duration-300) var(--ease-out),
              box-shadow var(--duration-300) var(--ease-out),
              border-color var(--duration-300) var(--ease-out);
}

.notely-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--notely-shadow-xl);
  border-color: var(--notely-primary-light);
}

.notely-card-header {
  padding: var(--notely-spacing-lg);
  border-bottom: var(--border-width-1) solid var(--notely-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notely-card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--notely-text-primary);
  margin: 0;
}

.notely-card-body {
  padding: var(--notely-spacing-lg);
}

.notely-card-footer {
  padding: var(--notely-spacing-md) var(--notely-spacing-lg);
  border-top: var(--border-width-1) solid var(--notely-border);
  background-color: var(--notely-surface-hover);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Notely navigation */
.notely-nav {
  display: flex;
  align-items: center;
  gap: var(--notely-spacing-sm);
  padding: var(--notely-spacing-sm);
  background-color: var(--notely-surface);
  border-radius: var(--notely-radius-xl);
  border: var(--border-width-1) solid var(--notely-border);
}

.notely-nav-item {
  display: flex;
  align-items: center;
  gap: var(--notely-spacing-sm);
  padding: var(--notely-spacing-sm) var(--notely-spacing-md);
  border-radius: var(--notely-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--notely-text-secondary);
  text-decoration: none;
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
}

.notely-nav-item:hover {
  background-color: var(--notely-surface-hover);
  color: var(--notely-text-primary);
}

.notely-nav-item.active {
  background-color: var(--notely-primary);
  color: white;
}

/* Notely forms */
.notely-form-group {
  margin-bottom: var(--notely-spacing-md);
}

.notely-form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--notely-text-primary);
  margin-bottom: var(--notely-spacing-sm);
}

.notely-form-input {
  width: 100%;
  padding: var(--notely-spacing-sm) var(--notely-spacing-md);
  border: var(--border-width-1) solid var(--notely-border);
  border-radius: var(--notely-radius-md);
  background-color: var(--notely-surface);
  color: var(--notely-text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: border-color var(--duration-200) var(--ease-out),
              box-shadow var(--duration-200) var(--ease-out);
}

.notely-form-input:focus {
  outline: none;
  border-color: var(--notely-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.notely-form-input::placeholder {
  color: var(--notely-text-tertiary);
}

/* Notely badges */
.notely-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--notely-spacing-xs) var(--notely-spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-none);
  border-radius: var(--notely-radius-full);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.notely-badge-primary {
  background-color: var(--notely-primary);
  color: white;
}

.notely-badge-accent {
  background-color: var(--notely-accent);
  color: white;
}

.notely-badge-secondary {
  background-color: var(--notely-surface-hover);
  color: var(--notely-text-secondary);
  border: var(--border-width-1) solid var(--notely-border);
}

/* Notely alerts */
.notely-alert {
  padding: var(--notely-spacing-md);
  border-radius: var(--notely-radius-md);
  margin-bottom: var(--notely-spacing-md);
  border: var(--border-width-1) solid transparent;
}

.notely-alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
  color: var(--notely-success);
}

.notely-alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
  color: var(--notely-warning);
}

.notely-alert-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  color: var(--notely-error);
}

.notely-alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  color: var(--notely-info);
}

/* ===== NOTELY UTILITY CLASSES ===== */

/* Spacing utilities */
.notely-spacing-xs { margin: var(--notely-spacing-xs); }
.notely-spacing-sm { margin: var(--notely-spacing-sm); }
.notely-spacing-md { margin: var(--notely-spacing-md); }
.notely-spacing-lg { margin: var(--notely-spacing-lg); }
.notely-spacing-xl { margin: var(--notely-spacing-xl); }

.notely-padding-xs { padding: var(--notely-spacing-xs); }
.notely-padding-sm { padding: var(--notely-spacing-sm); }
.notely-padding-md { padding: var(--notely-spacing-md); }
.notely-padding-lg { padding: var(--notely-spacing-lg); }
.notely-padding-xl { padding: var(--notely-spacing-xl); }

/* Text utilities */
.notely-text-primary { color: var(--notely-text-primary); }
.notely-text-secondary { color: var(--notely-text-secondary); }
.notely-text-tertiary { color: var(--notely-text-tertiary); }
.notely-text-accent { color: var(--notely-accent); }

/* Background utilities */
.notely-bg { background-color: var(--notely-bg); }
.notely-bg-surface { background-color: var(--notely-surface); }
.notely-bg-surface-hover { background-color: var(--notely-surface-hover); }
.notely-bg-primary { background-color: var(--notely-primary); }
.notely-bg-accent { background-color: var(--notely-accent); }

/* Border utilities */
.notely-border { border: var(--border-width-1) solid var(--notely-border); }
.notely-border-primary { border-color: var(--notely-primary); }
.notely-border-accent { border-color: var(--notely-accent); }

.notely-rounded-sm { border-radius: var(--notely-radius-sm); }
.notely-rounded-md { border-radius: var(--notely-radius-md); }
.notely-rounded-lg { border-radius: var(--notely-radius-lg); }
.notely-rounded-xl { border-radius: var(--notely-radius-xl); }
.notely-rounded-full { border-radius: var(--notely-radius-full); }

/* Shadow utilities */
.notely-shadow-sm { box-shadow: var(--notely-shadow-sm); }
.notely-shadow-md { box-shadow: var(--notely-shadow-md); }
.notely-shadow-lg { box-shadow: var(--notely-shadow-lg); }
.notely-shadow-xl { box-shadow: var(--notely-shadow-xl); }

/* Transition utilities */
.notely-transition {
  transition: all var(--duration-200) var(--ease-out);
}

.notely-transition-colors {
  transition: color var(--duration-200) var(--ease-out),
              background-color var(--duration-200) var(--ease-out),
              border-color var(--duration-200) var(--ease-out);
}

.notely-transition-transform {
  transition: transform var(--duration-200) var(--ease-out);
}

/* Hover utilities */
.notely-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--notely-shadow-lg);
}

.notely-hover-scale:hover {
  transform: scale(1.02);
}

.notely-hover-primary:hover {
  background-color: var(--notely-primary);
  color: white;
}

.notely-hover-accent:hover {
  background-color: var(--notely-accent);
  color: white;
}

/* ===== NOTELY ANIMATIONS ===== */

/* Breathing animation for subtle movement */
.notely-breathing {
  animation: notely-breathing 3s ease-in-out infinite;
}

@keyframes notely-breathing {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

/* Pulse animation for attention */
.notely-pulse {
  animation: notely-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes notely-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Fade in animation */
.notely-fade-in {
  animation: notely-fade-in var(--duration-500) var(--ease-out);
}

@keyframes notely-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Slide up animation */
.notely-slide-up {
  animation: notely-slide-up var(--duration-500) var(--ease-out);
}

@keyframes notely-slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Stagger animation for lists */
.notely-stagger-children > * {
  animation: notely-slide-up var(--duration-500) var(--ease-out);
}

.notely-stagger-children > *:nth-child(1) { animation-delay: 0ms; }
.notely-stagger-children > *:nth-child(2) { animation-delay: 100ms; }
.notely-stagger-children > *:nth-child(3) { animation-delay: 200ms; }
.notely-stagger-children > *:nth-child(4) { animation-delay: 300ms; }
.notely-stagger-children > *:nth-child(5) { animation-delay: 400ms; }
.notely-stagger-children > *:nth-child(6) { animation-delay: 500ms; }

/* ===== NOTELY RESPONSIVE UTILITIES ===== */

/* Mobile-first responsive classes */
@media (max-width: 767px) {
  .notely-mobile-stack {
    flex-direction: column;
  }
  
  .notely-mobile-full {
    width: 100%;
  }
  
  .notely-mobile-center {
    text-align: center;
  }
  
  .notely-mobile-hidden {
    display: none;
  }
}

@media (min-width: 768px) {
  .notely-tablet-row {
    flex-direction: row;
  }
  
  .notely-tablet-hidden {
    display: none;
  }
}

@media (min-width: 1024px) {
  .notely-desktop-grid {
    display: grid;
  }
  
  .notely-desktop-hidden {
    display: none;
  }
}

/* ===== NOTELY ACCESSIBILITY ===== */

/* Focus styles */
.notely-focus:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.4);
  border-radius: var(--notely-radius-sm);
}

/* Skip links */
.notely-skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--notely-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--notely-radius-sm);
  z-index: 1000;
  transition: top var(--duration-200) var(--ease-out);
}

.notely-skip-link:focus {
  top: 6px;
}

/* Screen reader only */
.notely-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  .notely-breathing,
  .notely-pulse,
  .notely-fade-in,
  .notely-slide-up,
  .notely-stagger-children > * {
    animation: none;
  }
  
  .notely-transition,
  .notely-transition-colors,
  .notely-transition-transform,
  .notely-btn,
  .notely-card,
  .notely-nav-item {
    transition: none;
  }
  
  .notely-hover-lift:hover,
  .notely-hover-scale:hover {
    transform: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (forced-colors: active) {
  .notely-btn,
  .notely-card,
  .notely-nav-item,
  .notely-form-input,
  .notely-badge,
  .notely-alert {
    border: 1px solid ButtonText;
  }
  
  .notely-btn-primary,
  .notely-btn-accent,
  .notely-badge-primary,
  .notely-badge-accent {
    background-color: ButtonFace;
    color: ButtonText;
  }
  
  .notely-nav-item.active {
    background-color: Highlight;
    color: HighlightText;
  }
}