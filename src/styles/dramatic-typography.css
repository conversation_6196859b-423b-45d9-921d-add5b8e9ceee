/* DRAMATIC Typography Changes - Will be immediately noticeable */

/* Import Montserrat font for a completely different look */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap');

/* Apply Montserrat font to EVERYTHING */
body * {
  font-family: 'Montserrat', sans-serif !important;
}

/* Make post cards stand out dramatically */
.post-card {
  border-radius: 16px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
  border-left-width: 6px !important;
  border-top-width: 0 !important;
  padding: 20px !important;
  margin-bottom: 24px !important;
}

/* Make author names MUCH larger */
.post-card .font-semibold,
.post-card .notely-heading,
.post-card .author-name {
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.01em !important;
  margin-bottom: 4px !important;
  color: #ff6b6b !important; /* Bright coral color for dramatic effect */
}

/* Make content text larger and more readable */
.post-card .px-4.py-3,
.post-card .post-content {
  font-size: 16px !important;
  line-height: 1.8 !important;
  letter-spacing: 0.01em !important;
  padding: 16px 8px !important;
}

/* Make thread indicators pop */
.post-card .bg-red-500 {
  font-weight: 600 !important;
  padding: 8px 12px !important;
  border-radius: 16px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
  background-color: #ff6b6b !important; /* Match the author name color */
}

/* Make platform tabs more distinct */
.notely-pill {
  font-weight: 500 !important;
  font-size: 14px !important;
  padding: 8px 16px !important;
  border-radius: 9999px !important;
}

/* Make daily wisdom quote stand out */
.notely-quote {
  font-family: 'Georgia', serif !important;
  font-style: italic !important;
  font-size: 18px !important;
  line-height: 1.6 !important;
  padding: 16px !important;
  border-left: 4px solid #74c0fc !important;
  background-color: rgba(116, 192, 252, 0.05) !important;
}

/* Change the platform indicator from top border to left border */
.post-card[class*="border-t-"] {
  border-top-width: 0 !important;
  border-left-width: 6px !important;
}

/* X/Twitter */
.post-card[class*="border-t-black"] {
  border-left-color: black !important;
  border-top-color: transparent !important;
}

/* LinkedIn */
.post-card[class*="border-t-blue-600"] {
  border-left-color: #0077b5 !important;
  border-top-color: transparent !important;
}

/* Reddit */
.post-card[class*="border-t-orange-500"] {
  border-left-color: #ff4500 !important;
  border-top-color: transparent !important;
}

/* Instagram */
.post-card[class*="border-t-purple-500"] {
  border-left-color: #e1306c !important;
  border-top-color: transparent !important;
}

/* Pinterest */
.post-card[class*="border-t-red-600"] {
  border-left-color: #e60023 !important;
  border-top-color: transparent !important;
}

/* Web */
.post-card[class*="border-t-green-500"] {
  border-left-color: #10b981 !important;
  border-top-color: transparent !important;
}

/* Add dramatic spacing between elements */
.post-card > div {
  margin-bottom: 12px !important;
}

/* Make the search bar stand out */
input[type="search"],
input[type="text"] {
  font-family: 'Montserrat', sans-serif !important;
  font-size: 16px !important;
  padding: 12px 16px !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Make buttons more prominent */
button {
  font-family: 'Montserrat', sans-serif !important;
  font-weight: 600 !important;
  letter-spacing: 0.02em !important;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

/* Make the storage section more visible */
.storage-usage {
  font-family: 'Montserrat', sans-serif !important;
  font-size: 14px !important;
  padding: 16px !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Add a subtle animation to post cards on hover */
.post-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease !important;
}