/* 
 * Dashboard Styles for Notely
 * Main dashboard layout and header styling
 */

.dashboard-container {
  min-height: 100vh;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-family: var(--font-sans);
}

.dashboard-header {
  padding: var(--space-6) var(--space-4) var(--space-4);
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: var(--z-40);
  backdrop-filter: blur(10px);
  background-color: rgba(var(--color-surface), 0.95);
}

.dashboard-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  letter-spacing: var(--letter-spacing-tight);
  text-align: center;
}

.dashboard-loading,
.dashboard-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.dashboard-error {
  color: var(--color-error);
}

/* Card background override for proper theming */
.post-card {
  background-color: var(--color-surface) !important;
  border-color: var(--color-border) !important;
}

/* Ensure card background variables are defined */
:root {
  --card-background: var(--color-surface);
  --card-border-color: var(--color-border);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-header {
    padding: var(--space-4) var(--space-3) var(--space-3);
  }
  
  .dashboard-title {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: var(--space-3) var(--space-2) var(--space-2);
  }
  
  .dashboard-title {
    font-size: var(--font-size-xl);
  }
}