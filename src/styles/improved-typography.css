/* DRAMATIC Typography Changes */

/* Import Montserrat font for a completely different look */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap');

/* Apply Montserrat font to everything */
* {
  font-family: 'Montserrat', sans-serif !important;
}

/* Make post cards stand out dramatically */
.post-card {
  border-radius: 16px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
  border-left-width: 6px !important;
  border-top-width: 0 !important;
  padding: 20px !important;
  margin-bottom: 24px !important;
}

/* Make author names much larger */
.notely-heading {
  font-size: 18px !important;
  font-weight: 700 !important;
  letter-spacing: -0.01em !important;
  margin-bottom: 4px !important;
}

/* Make content text larger and more readable */
.post-card .px-4.py-3 {
  font-size: 16px !important;
  line-height: 1.7 !important;
  letter-spacing: 0.01em !important;
  padding: 16px 8px !important;
}

/* Make thread indicators pop */
.bg-red-500 {
  font-weight: 600 !important;
  padding: 8px 12px !important;
  border-radius: 16px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Make platform tabs more distinct */
.notely-pill {
  font-weight: 500 !important;
  font-size: 14px !important;
  padding: 8px 16px !important;
  border-radius: 9999px !important;
}

/* Make daily wisdom quote stand out */
.notely-quote {
  font-family: 'Georgia', serif !important;
  font-style: italic !important;
  font-size: 18px !important;
  line-height: 1.6 !important;
  padding: 16px !important;
  border-left: 4px solid #74c0fc !important;
  background-color: rgba(116, 192, 252, 0.05) !important;
}