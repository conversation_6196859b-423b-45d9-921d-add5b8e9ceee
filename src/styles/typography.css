/* 
 * Typography System
 * Main entry point for typography styles
 */

/* Import typography components */
@import './design-system/typography.css';
@import './design-system/text-truncation.css';

/* 
 * Additional Typography Enhancements
 * These styles enhance the base typography system with specific improvements
 * for the Notely extension.
 */

/* Card Title Typography */
.card-title {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--space-2);
}

/* Card Content Typography */
.card-content p {
  font-family: var(--font-family-base);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-3);
}

/* Card Metadata Typography */
.card-metadata {
  font-family: var(--font-family-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
  color: var(--color-text-tertiary);
}

/* Platform Tab Typography */
.platform-tab {
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

/* Tag Typography */
.tag {
  font-family: var(--font-family-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

/* Button Typography */
.btn {
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-none);
  letter-spacing: var(--letter-spacing-normal);
}

/* Input Typography */
input, textarea, select {
  font-family: var(--font-family-base);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

/* Link Typography */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* List Typography */
ul, ol {
  padding-left: var(--space-6);
  margin: var(--space-4) 0;
}

li {
  margin-bottom: var(--space-2);
}

/* Blockquote Typography */
blockquote {
  font-family: var(--font-family-base);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  font-style: italic;
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-normal);
  color: var(--color-text-secondary);
  padding-left: var(--space-4);
  border-left: 2px solid var(--color-border);
  margin: var(--space-4) 0;
}

/* Code Typography */
code {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  background-color: var(--color-surface-hover);
  padding: 0.2em 0.4em;
  border-radius: var(--radius-sm);
}

/* Heading Margins */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
}

h1 {
  margin-bottom: var(--space-6);
}

h2 {
  margin-bottom: var(--space-5);
}

h3 {
  margin-bottom: var(--space-4);
}

h4, h5, h6 {
  margin-bottom: var(--space-3);
}

/* Paragraph Margins */
p {
  margin-top: 0;
  margin-bottom: var(--space-4);
}

/* Small Text */
small {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-tertiary);
}

/* Text Selection */
::selection {
  background-color: rgba(99, 102, 241, 0.2);
  color: var(--color-text-primary);
}