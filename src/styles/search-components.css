/* 
 * Search Components
 * Enhanced search elements with improved accessibility, visual design, and interaction states
 */

/* ===== SEARCH CONTAINER ===== */

.search-container {
  position: relative;
  width: 100%;
  margin-bottom: var(--space-4);
}

/* ===== SEARCH INPUT ===== */

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-8) var(--space-3) var(--space-8);
  border-radius: var(--radius-xl);
  border: var(--border-width-2) solid var(--input-border);
  background-color: var(--input-background);
  color: var(--input-text);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  transition: all var(--duration-200) var(--ease-out);
  box-shadow: var(--shadow-sm);
}

/* Search input hover state */
.search-input:hover:not(:focus):not(:disabled) {
  border-color: var(--color-border-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Search input focus state */
.search-input:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px var(--focus-ring-color);
  transform: translateY(-1px);
}

/* Search input placeholder */
.search-input::placeholder {
  color: var(--input-placeholder);
  opacity: 0.8;
}

/* Search input disabled state */
.search-input:disabled {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-200);
  color: var(--color-gray-500);
  cursor: not-allowed;
  box-shadow: none;
}

/* ===== SEARCH ICON ===== */

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: var(--color-text-tertiary);
  pointer-events: none;
  transition: color var(--duration-200) var(--ease-out);
}

/* Search icon when input is focused */
.search-input:focus ~ .search-icon {
  color: var(--color-primary);
}

/* ===== SEARCH CLEAR BUTTON ===== */

.search-clear {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--color-text-tertiary);
  background-color: var(--color-gray-200);
  border: none;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all var(--duration-200) var(--ease-out);
  padding: 0;
}

/* Show clear button when input has content */
.search-input:not(:placeholder-shown) ~ .search-clear {
  opacity: 1;
}

/* Clear button hover state */
.search-clear:hover {
  background-color: var(--color-gray-300);
  color: var(--color-text-primary);
  transform: translateY(-50%) scale(1.1);
}

/* Clear button focus state */
.search-clear:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-background), 0 0 0 4px var(--focus-ring-color);
}

/* Clear button pressed state */
.search-clear:active {
  transform: translateY(-50%) scale(0.95);
}

/* ===== SEARCH LOADING STATE ===== */

.search-loading {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--duration-200) var(--ease-out);
  pointer-events: none;
}

/* Show loading indicator when search is in progress */
.search-loading.active {
  opacity: 1;
}

/* Loading dots animation */
.search-loading-dots {
  display: flex;
  align-items: center;
  gap: 4px;
}

.search-loading-dot {
  width: 6px;
  height: 6px;
  border-radius: var(--radius-full);
  background-color: var(--color-primary);
  animation: search-loading-dot 1.4s infinite ease-in-out both;
}

.search-loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.search-loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes search-loading-dot {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* ===== SEARCH RESULTS DROPDOWN ===== */

.search-results {
  position: absolute;
  top: calc(100% + var(--space-2));
  left: 0;
  right: 0;
  background-color: var(--color-surface);
  border: var(--border-width-1) solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: 300px;
  overflow-y: auto;
  z-index: var(--z-30);
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
  transition: all var(--duration-200) var(--ease-out);
}

/* Show search results when active */
.search-results.active {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* Search result item */
.search-result-item {
  padding: var(--space-3);
  border-bottom: var(--border-width-1) solid var(--color-border);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: var(--color-surface-hover);
}

.search-result-item:focus-visible {
  outline: none;
  background-color: var(--color-surface-hover);
  box-shadow: inset 0 0 0 2px var(--focus-ring-color);
}

/* Search result highlight */
.search-result-highlight {
  background-color: rgba(99, 102, 241, 0.2);
  padding: 0 2px;
  border-radius: var(--radius-sm);
}

/* Empty search results */
.search-no-results {
  padding: var(--space-4);
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* ===== SEARCH VARIANTS ===== */

/* Compact search */
.search-container.compact .search-input {
  padding: var(--space-2) var(--space-6) var(--space-2) var(--space-6);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-lg);
}

.search-container.compact .search-icon,
.search-container.compact .search-clear {
  width: 16px;
  height: 16px;
}

/* Expanded search (for header/prominent placement) */
.search-container.expanded .search-input {
  padding: var(--space-4) var(--space-10) var(--space-4) var(--space-10);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-2xl);
}

.search-container.expanded .search-icon,
.search-container.expanded .search-clear {
  width: 24px;
  height: 24px;
}

.search-container.expanded .search-icon {
  left: var(--space-4);
}

.search-container.expanded .search-clear {
  right: var(--space-4);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 768px) {
  .search-input {
    font-size: var(--font-size-sm);
    padding: var(--space-2) var(--space-6) var(--space-2) var(--space-6);
  }
  
  .search-container.expanded .search-input {
    padding: var(--space-3) var(--space-8) var(--space-3) var(--space-8);
    font-size: var(--font-size-md);
  }
}

@media (max-width: 480px) {
  .search-results {
    max-height: 250px;
  }
}

/* ===== ACCESSIBILITY ADJUSTMENTS ===== */

@media (prefers-reduced-motion: reduce) {
  .search-input,
  .search-clear,
  .search-results,
  .search-result-item {
    transition: none;
  }
  
  .search-input:hover:not(:focus):not(:disabled) {
    transform: none;
  }
  
  .search-input:focus {
    transform: none;
  }
  
  .search-clear:hover {
    transform: translateY(-50%);
  }
  
  .search-clear:active {
    transform: translateY(-50%);
  }
  
  .search-loading-dot {
    animation: none;
  }
  
  .search-loading.active .search-loading-dot:nth-child(1) {
    opacity: 0.2;
  }
  
  .search-loading.active .search-loading-dot:nth-child(2) {
    opacity: 0.5;
  }
  
  .search-loading.active .search-loading-dot:nth-child(3) {
    opacity: 0.8;
  }
}

/* ===== HIGH CONTRAST MODE ADJUSTMENTS ===== */

@media (forced-colors: active) {
  .search-input {
    border: 2px solid ButtonText;
  }
  
  .search-input:focus {
    outline: 2px solid Highlight;
    border-color: Highlight;
  }
  
  .search-clear {
    border: 1px solid ButtonText;
  }
  
  .search-results {
    border: 1px solid ButtonText;
  }
  
  .search-result-item {
    border-bottom: 1px solid ButtonText;
  }
  
  .search-result-highlight {
    border: 1px solid Highlight;
  }
}