/* Override Tailwind's base styles with higher specificity */
@layer base {
  html {
    --color-primary: #4f46e5 !important;
    --color-primary-light: #6366f1 !important;
    --color-primary-dark: #4338ca !important;
  }
}

/* Post Card Styles with high specificity */
#root .post-card,
#root article.post-card, 
#root .post-card,
#root [class*="post-card"],
#root [class^="post-card"],
#root [class*="postCard"],
#root [class^="postCard"] {
  background: linear-gradient(135deg, #1e1e1e 0%, #181818 50%, #161616 100%) !important;
  border: 1px solid #2F2F2F !important;
  border-radius: 1.25rem !important;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.08) !important;
  transition: all 0.3s ease-out !important;
  margin-bottom: 1.5rem !important;
  break-inside: avoid !important;
  backdrop-filter: blur(4px) !important;
  overflow: hidden !important;
}

/* Platform-specific top borders */
#root .post-card[data-platform="X/Twitter"],
#root article.post-card[data-platform="X/Twitter"],
#root [class*="post-card"][data-platform="X/Twitter"],
#root [class^="post-card"][data-platform="X/Twitter"],
#root [class*="postCard"][data-platform="X/Twitter"],
#root [class^="postCard"][data-platform="X/Twitter"] {
  border-top: 6px solid #1da1f2 !important;
}

#root .post-card[data-platform="LinkedIn"],
#root article.post-card[data-platform="LinkedIn"],
#root [class*="post-card"][data-platform="LinkedIn"],
#root [class^="post-card"][data-platform="LinkedIn"],
#root [class*="postCard"][data-platform="LinkedIn"],
#root [class^="postCard"][data-platform="LinkedIn"] {
  border-top: 6px solid #0077b5 !important;
}

#root .post-card[data-platform="Reddit"],
#root article.post-card[data-platform="Reddit"],
#root [class*="post-card"][data-platform="Reddit"],
#root [class^="post-card"][data-platform="Reddit"],
#root [class*="postCard"][data-platform="Reddit"],
#root [class^="postCard"][data-platform="Reddit"] {
  border-top: 6px solid #ff4500 !important;
}

#root .post-card[data-platform="Instagram"],
#root article.post-card[data-platform="Instagram"],
#root [class*="post-card"][data-platform="Instagram"],
#root [class^="post-card"][data-platform="Instagram"],
#root [class*="postCard"][data-platform="Instagram"],
#root [class^="postCard"][data-platform="Instagram"] {
  border-top: 6px solid #e1306c !important;
}

#root .post-card[data-platform="pinterest"],
#root article.post-card[data-platform="pinterest"],
#root [class*="post-card"][data-platform="pinterest"],
#root [class^="post-card"][data-platform="pinterest"],
#root [class*="postCard"][data-platform="pinterest"],
#root [class^="postCard"][data-platform="pinterest"] {
  border-top: 6px solid #e60023 !important;
}

#root .post-card[data-platform="Web"],
#root article.post-card[data-platform="Web"],
#root [class*="post-card"][data-platform="Web"],
#root [class^="post-card"][data-platform="Web"],
#root [class*="postCard"][data-platform="Web"],
#root [class^="postCard"][data-platform="Web"] {
  border-top: 6px solid #10b981 !important;
}

/* Hover effects */
#root .post-card:hover,
#root article.post-card:hover,
#root [class*="post-card"]:hover,
#root [class^="post-card"]:hover,
#root [class*="postCard"]:hover,
#root [class^="postCard"]:hover {
  transform: translateY(-0.25rem) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* Avatar background image */
#root .post-card .avatar-bg {
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* Author name text */
#root .post-card .author-name {
  font-size: 1.125rem !important;
  letter-spacing: -0.01em !important;
}

/* Image container */
#root .post-card .post-image-container {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* Post image */
#root .post-card .post-image {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-position: center center !important;
}

/* For capture mode */
#root .post-card .post-image.capture-mode {
  object-fit: contain !important;
}

/* For normal mode */
#root .post-card .post-image:not(.capture-mode) {
  object-fit: cover !important;
}

/* Card content */
.post-card .post-content {
  padding: 0.75rem 1rem !important;
  color: #f3f4f6 !important;
  line-height: 1.6 !important;
  font-size: 0.9375rem !important;
  word-break: break-word !important;
}

/* Author section */
.post-card .author-name {
  font-size: 1.125rem !important;
  font-weight: 700 !important;
  color: #f9fafb !important;
  letter-spacing: -0.01em !important;
  margin: 0 !important;
  line-height: 1.2 !important;
}

.post-card .author-handle,
.post-card .timestamp {
  font-size: 0.875rem !important;
  color: #9ca3af !important;
}

.post-card .timestamp:hover {
  color: #d1d5db !important;
}
