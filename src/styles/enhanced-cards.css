/* 
 * Enhanced Card Design System for Notely
 * A more visually appealing and consistent card system with better hierarchy and interaction
 */

/* ===== CARD BASE STYLES ===== */

.post-card {
  background-color: var(--card-background);
  border: var(--border-width-1) solid var(--card-border-color);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  transition: transform var(--duration-300) var(--ease-out),
              box-shadow var(--duration-300) var(--ease-out),
              border-color var(--duration-300) var(--ease-out);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  /* Add subtle gradient background for more visual interest */
  background-image: linear-gradient(
    to bottom,
    var(--card-background),
    rgba(249, 250, 251, 0.8)
  );
}

/* Card hover state */
.post-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-light);
}

/* Card focus state */
.post-card:focus-within {
  outline: none;
  box-shadow: var(--shadow-lg), 0 0 0 3px var(--focus-ring-color);
}

/* ===== PLATFORM INDICATORS ===== */

/* Platform indicator - top accent bar */
.platform-indicator-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  z-index: 1;
}

/* Platform-specific colors */
.platform-indicator-top.twitter {
  background-color: var(--color-twitter);
}

.platform-indicator-top.linkedin {
  background-color: var(--color-linkedin);
}

.platform-indicator-top.reddit {
  background-color: var(--color-reddit);
}

.platform-indicator-top.instagram {
  background-color: var(--color-instagram);
}

.platform-indicator-top.pinterest {
  background-color: var(--color-pinterest);
}

.platform-indicator-top.web {
  background-color: var(--color-web);
}

/* ===== CARD HEADER ===== */

.card-header {
  padding: var(--space-6) var(--space-6) var(--space-4);
  display: flex;
  align-items: center;
  position: relative;
  border-bottom: none;
}

/* Avatar styling */
.card-header-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  margin-right: var(--space-4);
  object-fit: cover;
  border: 2px solid white;
  box-shadow: var(--shadow-sm);
  background-color: var(--color-surface-hover);
  flex-shrink: 0;
}

/* Header content container */
.card-header-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Card title */
.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
}

/* Card subtitle */
.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  line-height: var(--line-height-normal);
}

/* Card timestamp */
.card-timestamp {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* Platform badge in header */
.card-platform-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin-left: auto;
  background-color: var(--color-surface-hover);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
}

/* Platform-specific badges */
.card-platform-badge.twitter {
  background-color: rgba(29, 161, 242, 0.1);
  border-color: rgba(29, 161, 242, 0.3);
  color: var(--color-twitter);
}

.card-platform-badge.linkedin {
  background-color: rgba(0, 119, 181, 0.1);
  border-color: rgba(0, 119, 181, 0.3);
  color: var(--color-linkedin);
}

.card-platform-badge.reddit {
  background-color: rgba(255, 69, 0, 0.1);
  border-color: rgba(255, 69, 0, 0.3);
  color: var(--color-reddit);
}

.card-platform-badge.instagram {
  background-color: rgba(225, 48, 108, 0.1);
  border-color: rgba(225, 48, 108, 0.3);
  color: var(--color-instagram);
}

.card-platform-badge.pinterest {
  background-color: rgba(230, 0, 35, 0.1);
  border-color: rgba(230, 0, 35, 0.3);
  color: var(--color-pinterest);
}

.card-platform-badge.web {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
  color: var(--color-web);
}

/* ===== CARD CONTENT ===== */

.card-content {
  padding: var(--space-4) var(--space-6) var(--space-6);
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  position: relative;
}

/* Content divider */
.card-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: var(--space-6);
  right: var(--space-6);
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    var(--color-border),
    transparent
  );
}

/* Card text */
.card-text {
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-clamp: 3;
  margin-bottom: var(--space-2);
}

/* Card text highlight */
.card-text-highlight {
  background-color: rgba(99, 102, 241, 0.1);
  color: var(--color-primary-dark);
  padding: 0 var(--space-1);
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
}

/* ===== CARD MEDIA ===== */

.card-media-container {
  position: relative;
  width: 100%;
  margin-top: var(--space-2);
  border-radius: var(--radius-xl);
  overflow: hidden;
  background-color: var(--color-surface-hover);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
}

/* Media aspect ratios */
.card-media-container.aspect-16-9 {
  aspect-ratio: 16/9;
}

.card-media-container.aspect-1-1 {
  aspect-ratio: 1/1;
}

.card-media-container.aspect-4-5 {
  aspect-ratio: 4/5;
}

.card-media-container.aspect-3-2 {
  aspect-ratio: 3/2;
}

/* Media image */
.card-media {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--duration-500) var(--ease-out),
              filter var(--duration-300) var(--ease-out);
}

/* Media hover effect */
.card-media-container:hover .card-media {
  transform: scale(1.05);
  filter: brightness(1.05);
}

/* Media caption */
.card-media-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--space-2) var(--space-3);
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* ===== TAGS SECTION ===== */

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.card-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  background-color: var(--color-surface-hover);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
}

.card-tag:hover {
  background-color: rgba(99, 102, 241, 0.1);
  border-color: var(--color-primary-light);
  color: var(--color-primary);
  transform: translateY(-1px);
}

/* ===== CARD FOOTER ===== */

.card-footer {
  padding: var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(249, 250, 251, 0.8);
  border-top: 1px solid var(--color-border);
  position: relative;
}

/* Footer gradient overlay */
.card-footer::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(249, 250, 251, 0.8), transparent);
  pointer-events: none;
}

/* Metadata row */
.metadata-row {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  min-height: 24px;
}

/* Metadata item */
.metadata-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  transition: all var(--duration-200) var(--ease-in-out);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  background-color: transparent;
}

.metadata-item:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-text-primary);
}

/* Metadata item icons */
.metadata-item-icon {
  width: 18px;
  height: 18px;
  color: currentColor;
  opacity: 0.8;
}

/* Metadata item count */
.metadata-item-count {
  font-weight: var(--font-weight-medium);
}

/* Action buttons */
.action-buttons {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.action-button {
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  transition: all var(--duration-200) var(--ease-out);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 36px;
  height: 36px;
  position: relative;
  overflow: hidden;
}

/* Action button hover state */
.action-button:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-primary);
  border-color: var(--color-primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Action button focus state */
.action-button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--focus-ring-color);
}

/* Action button pressed state */
.action-button:active {
  transform: translateY(0);
}

/* Action button ripple effect */
.action-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  background-color: rgba(255, 255, 255, 0.4);
  opacity: 0;
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: opacity 0.5s, transform 0.5s;
}

.action-button:active::after {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  transition: opacity 0s, transform 0s;
}

/* Action button tooltip */
.action-button-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  background-color: var(--color-gray-800);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--duration-200) var(--ease-out),
              transform var(--duration-200) var(--ease-out);
  z-index: 10;
}

.action-button:hover .action-button-tooltip {
  opacity: 1;
  transform: translateX(-50%) translateY(-4px);
}

/* ===== SPECIAL CARD TYPES ===== */

/* Quote card */
.post-card.quote-card {
  background-color: rgba(99, 102, 241, 0.05);
  border-left: 4px solid var(--color-primary);
}

.post-card.quote-card .card-text {
  font-style: italic;
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-primary);
  padding: 0 var(--space-4);
  position: relative;
}

.post-card.quote-card .card-text::before {
  content: '"';
  font-size: 4rem;
  position: absolute;
  top: -2rem;
  left: -1rem;
  color: var(--color-primary-light);
  opacity: 0.2;
  font-family: Georgia, serif;
}

/* Featured card */
.post-card.featured-card {
  border: 2px solid var(--color-primary-light);
  background-image: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.05),
    rgba(255, 255, 255, 0.9)
  );
}

.post-card.featured-card::before {
  content: 'Featured';
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background-color: var(--color-primary);
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  z-index: 2;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 1024px) {
  .card-header {
    padding: var(--space-5) var(--space-5) var(--space-3);
  }
  
  .card-content {
    padding: var(--space-3) var(--space-5) var(--space-5);
  }
  
  .card-footer {
    padding: var(--space-3) var(--space-5);
  }
}

@media (max-width: 768px) {
  .card-header-avatar {
    width: 40px;
    height: 40px;
  }
  
  .card-title {
    font-size: var(--font-size-md);
  }
  
  .card-text {
    font-size: var(--font-size-sm);
  }
  
  .metadata-item {
    font-size: var(--font-size-xs);
  }
  
  .action-button {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .card-header {
    padding: var(--space-4) var(--space-4) var(--space-2);
  }
  
  .card-content {
    padding: var(--space-2) var(--space-4) var(--space-4);
  }
  
  .card-footer {
    padding: var(--space-2) var(--space-4);
  }
  
  .card-header-avatar {
    width: 36px;
    height: 36px;
    margin-right: var(--space-3);
  }
  
  .metadata-row {
    gap: var(--space-2);
  }
  
  .action-button {
    width: 28px;
    height: 28px;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

@media (prefers-reduced-motion: reduce) {
  .post-card,
  .card-media,
  .action-button,
  .card-tag,
  .metadata-item {
    transition: none;
  }
  
  .post-card:hover {
    transform: none;
  }
  
  .card-media-container:hover .card-media {
    transform: none;
  }
  
  .action-button:hover {
    transform: none;
  }
  
  .action-button::after {
    display: none;
  }
}

/* High contrast mode */
@media (forced-colors: active) {
  .post-card {
    border: 2px solid CanvasText;
  }
  
  .card-header,
  .card-footer {
    border-color: CanvasText;
  }
  
  .card-content::before {
    background: CanvasText;
  }
  
  .card-tag,
  .action-button,
  .card-platform-badge {
    border: 1px solid ButtonText;
  }
  
  .card-media-container {
    border: 1px solid CanvasText;
  }
}