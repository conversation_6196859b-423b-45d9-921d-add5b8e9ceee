/* 
 * Theme Switcher Component Styles
 */

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-in-out);
  padding: 0;
  margin: 0;
}

.theme-toggle:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.theme-toggle:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-background), 0 0 0 4px var(--focus-ring-color);
}

.theme-toggle svg {
  width: 20px;
  height: 20px;
  transition: transform var(--duration-200) var(--ease-in-out);
}

.theme-toggle:hover svg {
  transform: scale(1.1);
}

/* Animation for theme change */
.theme-toggle.theme-transition svg {
  animation: rotate-icon 500ms var(--ease-in-out);
}

@keyframes rotate-icon {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(0.8);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-toggle {
    width: 36px;
    height: 36px;
  }
  
  .theme-toggle svg {
    width: 18px;
    height: 18px;
  }
}