/* 
 * Tablet Styles - Optimized
 * Tablet-specific responsive design optimizations (768px - 1023px)
 */

@media (min-width: 768px) and (max-width: 1023px) {
  
  /* ===== LAYOUT ADJUSTMENTS ===== */
  .container {
    padding: 0 var(--space-6);
  }
  
  /* ===== GRID ADJUSTMENTS ===== */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  /* ===== COMPONENT ADJUSTMENTS ===== */
  
  /* Cards */
  .card-header {
    padding: var(--space-5);
  }
  
  .card-content {
    padding: var(--space-5);
  }
  
  .card-footer {
    padding: var(--space-4) var(--space-5);
  }
  
  .card-avatar {
    width: 44px;
    height: 44px;
  }
  
  /* Navigation */
  .nav {
    padding: var(--space-3);
    gap: var(--space-2);
  }
  
  .nav-item {
    padding: var(--space-3) var(--space-4);
  }
  
  /* Modals */
  .modal {
    max-width: 600px;
    margin: var(--space-8);
  }
  
  .modal-header {
    padding: var(--space-5);
  }
  
  .modal-body {
    padding: var(--space-5);
  }
  
  .modal-footer {
    padding: var(--space-5);
  }
  
  /* Dropdowns */
  .dropdown-menu {
    min-width: 14rem;
  }
  
  /* ===== TYPOGRAPHY ADJUSTMENTS ===== */
  .tablet-text-lg {
    font-size: var(--font-size-lg);
  }
  
  .tablet-text-xl {
    font-size: var(--font-size-xl);
  }
  
  .tablet-text-2xl {
    font-size: var(--font-size-2xl);
  }
  
  /* ===== SPACING ADJUSTMENTS ===== */
  .tablet-gap-4 { gap: var(--space-4); }
  .tablet-gap-6 { gap: var(--space-6); }
  .tablet-gap-8 { gap: var(--space-8); }
  
  .tablet-p-5 { padding: var(--space-5); }
  .tablet-p-6 { padding: var(--space-6); }
  .tablet-p-8 { padding: var(--space-8); }
  
  .tablet-px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
  .tablet-px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }
  
  .tablet-py-5 { padding-top: var(--space-5); padding-bottom: var(--space-5); }
  .tablet-py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
  
  .tablet-m-6 { margin: var(--space-6); }
  .tablet-m-8 { margin: var(--space-8); }
  
  .tablet-mx-6 { margin-left: var(--space-6); margin-right: var(--space-6); }
  .tablet-mx-8 { margin-left: var(--space-8); margin-right: var(--space-8); }
  
  .tablet-my-6 { margin-top: var(--space-6); margin-bottom: var(--space-6); }
  .tablet-my-8 { margin-top: var(--space-8); margin-bottom: var(--space-8); }
  
  /* ===== TABLET-SPECIFIC UTILITIES ===== */
  .tablet-hidden {
    display: none;
  }
  
  .tablet-block {
    display: block;
  }
  
  .tablet-flex {
    display: flex;
  }
  
  .tablet-grid {
    display: grid;
  }
  
  .tablet-flex-row {
    flex-direction: row;
  }
  
  .tablet-flex-col {
    flex-direction: column;
  }
  
  .tablet-items-center {
    align-items: center;
  }
  
  .tablet-justify-between {
    justify-content: space-between;
  }
  
  .tablet-justify-center {
    justify-content: center;
  }
  
  .tablet-text-center {
    text-align: center;
  }
  
  .tablet-text-left {
    text-align: left;
  }
  
  /* ===== TABLET LAYOUT PATTERNS ===== */
  .tablet-sidebar-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--space-6);
  }
  
  .tablet-two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
  }
  
  .tablet-three-column {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
  }
  
  /* ===== TABLET NAVIGATION ===== */
  .tablet-nav-horizontal {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4) var(--space-6);
    background-color: var(--color-surface);
    border-bottom: var(--border-width-1) solid var(--color-border);
  }
  
  .tablet-nav-item {
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
    text-decoration: none;
    transition: all var(--duration-200) var(--ease-out);
  }
  
  .tablet-nav-item:hover,
  .tablet-nav-item.active {
    background-color: var(--color-surface-hover);
    color: var(--color-primary);
  }
  
  /* ===== TABLET HEADER ===== */
  .tablet-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-6);
    background-color: var(--color-surface);
    border-bottom: var(--border-width-1) solid var(--color-border);
    position: sticky;
    top: 0;
    z-index: var(--z-30);
  }
  
  .tablet-header-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
  }
  
  .tablet-header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
  }
  
  /* ===== TABLET CONTENT AREAS ===== */
  .tablet-main-content {
    padding: var(--space-6);
    max-width: none;
  }
  
  .tablet-sidebar {
    padding: var(--space-6);
    background-color: var(--color-surface);
    border-right: var(--border-width-1) solid var(--color-border);
    height: 100vh;
    overflow-y: auto;
    position: sticky;
    top: 0;
  }
  
  .tablet-content-section {
    margin-bottom: var(--space-8);
  }
  
  .tablet-content-section:last-child {
    margin-bottom: 0;
  }
  
  /* ===== TABLET CARD LAYOUTS ===== */
  .tablet-card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-5);
  }
  
  .tablet-card-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
  }
  
  .tablet-card-masonry {
    columns: 2;
    column-gap: var(--space-5);
    column-fill: balance;
  }
  
  .tablet-card-masonry .card {
    break-inside: avoid;
    margin-bottom: var(--space-4);
  }
  
  /* ===== TABLET FORM LAYOUTS ===== */
  .tablet-form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }
  
  .tablet-form-full {
    grid-column: 1 / -1;
  }
  
  /* ===== TABLET MODAL ADJUSTMENTS ===== */
  .tablet-modal-large {
    max-width: 700px;
  }
  
  .tablet-modal-fullscreen {
    width: calc(100vw - 4rem);
    height: calc(100vh - 4rem);
    max-width: none;
    max-height: none;
  }
  
  /* ===== TABLET ANIMATIONS ===== */
  .tablet-slide-in-left {
    animation: tablet-slide-in-left var(--duration-300) var(--ease-out);
  }
  
  @keyframes tablet-slide-in-left {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .tablet-slide-in-right {
    animation: tablet-slide-in-right var(--duration-300) var(--ease-out);
  }
  
  @keyframes tablet-slide-in-right {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .tablet-fade-in-up {
    animation: tablet-fade-in-up var(--duration-400) var(--ease-out);
  }
  
  @keyframes tablet-fade-in-up {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* ===== TABLET PORTRAIT SPECIFIC ===== */
@media (min-width: 768px) and (max-width: 1023px) and (orientation: portrait) {
  .tablet-portrait-stack {
    flex-direction: column;
  }
  
  .tablet-portrait-full-width {
    width: 100%;
  }
  
  .tablet-portrait-center {
    text-align: center;
  }
  
  .tablet-sidebar-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .tablet-sidebar {
    height: auto;
    position: static;
    border-right: none;
    border-bottom: var(--border-width-1) solid var(--color-border);
  }
}

/* ===== TABLET LANDSCAPE SPECIFIC ===== */
@media (min-width: 768px) and (max-width: 1023px) and (orientation: landscape) {
  .tablet-landscape-row {
    flex-direction: row;
  }
  
  .tablet-landscape-sidebar {
    width: 240px;
    flex-shrink: 0;
  }
  
  .tablet-landscape-content {
    flex: 1;
    min-width: 0;
  }
  
  .tablet-card-masonry {
    columns: 3;
  }
  
  .tablet-three-column {
    grid-template-columns: repeat(4, 1fr);
  }
}