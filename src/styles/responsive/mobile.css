/* 
 * Mobile Styles - Optimized
 * Mobile-first responsive design optimizations
 */

/* Mobile-first base styles (up to 767px) */
@media (max-width: 767px) {
  
  /* ===== LAYOUT ADJUSTMENTS ===== */
  .container {
    padding: 0 var(--space-4);
  }
  
  /* ===== TYPOGRAPHY ADJUSTMENTS ===== */
  .text-3xl { font-size: var(--font-size-2xl); }
  .text-2xl { font-size: var(--font-size-xl); }
  .text-xl { font-size: var(--font-size-lg); }
  
  /* ===== COMPONENT ADJUSTMENTS ===== */
  
  /* Cards */
  .card {
    margin-bottom: var(--space-4);
  }
  
  .card-header {
    padding: var(--space-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .card-avatar {
    width: 40px;
    height: 40px;
  }
  
  .card-content {
    padding: var(--space-4);
  }
  
  .card-footer {
    padding: var(--space-3) var(--space-4);
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }
  
  /* Buttons */
  .btn {
    min-height: 44px; /* Touch target size */
    padding: var(--space-3) var(--space-4);
  }
  
  .btn-sm {
    min-height: 40px;
    padding: var(--space-2) var(--space-3);
  }
  
  .btn-lg {
    min-height: 48px;
    padding: var(--space-4) var(--space-6);
  }
  
  .btn-icon {
    min-width: 44px;
    min-height: 44px;
    padding: var(--space-3);
  }
  
  .btn-icon.btn-sm {
    min-width: 40px;
    min-height: 40px;
    padding: var(--space-2);
  }
  
  /* Navigation */
  .nav {
    flex-direction: column;
    gap: var(--space-1);
    padding: var(--space-3);
  }
  
  .nav-item {
    width: 100%;
    justify-content: flex-start;
    padding: var(--space-3) var(--space-4);
    min-height: 44px;
  }
  
  /* Forms */
  .form-input,
  .form-textarea,
  .form-select {
    min-height: 44px;
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base); /* Prevent zoom on iOS */
  }
  
  /* Modals */
  .modal {
    margin: var(--space-4);
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }
  
  .modal-header {
    padding: var(--space-4);
  }
  
  .modal-body {
    padding: var(--space-4);
  }
  
  .modal-footer {
    padding: var(--space-4);
    flex-direction: column-reverse;
    gap: var(--space-2);
  }
  
  .modal-footer .btn {
    width: 100%;
  }
  
  /* Dropdowns */
  .dropdown-menu {
    left: 0;
    right: 0;
    min-width: auto;
    max-width: calc(100vw - 2rem);
  }
  
  .dropdown-item {
    padding: var(--space-3) var(--space-4);
    min-height: 44px;
  }
  
  /* Toasts */
  .toast-container {
    bottom: var(--space-4);
    left: var(--space-4);
    right: var(--space-4);
  }
  
  .toast {
    min-width: auto;
    max-width: none;
  }
  
  /* ===== GRID ADJUSTMENTS ===== */
  .grid-auto-fit {
    grid-template-columns: 1fr;
  }
  
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: 1fr;
  }
  
  /* ===== SPACING ADJUSTMENTS ===== */
  .gap-6 { gap: var(--space-4); }
  .gap-8 { gap: var(--space-6); }
  
  .p-6 { padding: var(--space-4); }
  .p-8 { padding: var(--space-6); }
  
  .px-6 { padding-left: var(--space-4); padding-right: var(--space-4); }
  .px-8 { padding-left: var(--space-6); padding-right: var(--space-6); }
  
  .py-6 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
  .py-8 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
  
  .m-6 { margin: var(--space-4); }
  .m-8 { margin: var(--space-6); }
  
  .mx-6 { margin-left: var(--space-4); margin-right: var(--space-4); }
  .mx-8 { margin-left: var(--space-6); margin-right: var(--space-6); }
  
  .my-6 { margin-top: var(--space-4); margin-bottom: var(--space-4); }
  .my-8 { margin-top: var(--space-6); margin-bottom: var(--space-6); }
  
  /* ===== MOBILE-SPECIFIC UTILITIES ===== */
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full-width {
    width: 100%;
  }
  
  .mobile-center {
    text-align: center;
  }
  
  .mobile-stack {
    flex-direction: column;
  }
  
  .mobile-no-gap {
    gap: 0;
  }
  
  .mobile-small-gap {
    gap: var(--space-2);
  }
  
  /* ===== TOUCH OPTIMIZATIONS ===== */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Increase tap targets for better usability */
  a, button, [role="button"], [tabindex] {
    min-height: 44px;
    display: flex;
    align-items: center;
  }
  
  /* ===== SCROLL OPTIMIZATIONS ===== */
  .mobile-scroll-x {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .mobile-scroll-x::-webkit-scrollbar {
    display: none;
  }
  
  .mobile-scroll-y {
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
  }
  
  /* ===== SAFE AREA ADJUSTMENTS ===== */
  .mobile-safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .mobile-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .mobile-safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .mobile-safe-right {
    padding-right: env(safe-area-inset-right);
  }
  
  /* ===== MOBILE NAVIGATION ===== */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--color-surface);
    border-top: var(--border-width-1) solid var(--color-border);
    padding: var(--space-2) var(--space-4);
    padding-bottom: calc(var(--space-2) + env(safe-area-inset-bottom));
    z-index: var(--z-40);
  }
  
  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    color: var(--color-text-secondary);
    text-decoration: none;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    transition: all var(--duration-200) var(--ease-out);
    min-height: 44px;
    justify-content: center;
  }
  
  .mobile-nav-item:hover,
  .mobile-nav-item.active {
    background-color: var(--color-surface-hover);
    color: var(--color-primary);
  }
  
  .mobile-nav-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }
  
  /* ===== MOBILE HEADER ===== */
  .mobile-header {
    position: sticky;
    top: 0;
    background-color: var(--color-surface);
    border-bottom: var(--border-width-1) solid var(--color-border);
    padding: var(--space-3) var(--space-4);
    padding-top: calc(var(--space-3) + env(safe-area-inset-top));
    z-index: var(--z-30);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
  }
  
  .mobile-header-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin: 0;
  }
  
  .mobile-header-back {
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    color: var(--color-text-secondary);
    transition: all var(--duration-200) var(--ease-out);
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .mobile-header-back:hover {
    background-color: var(--color-surface-hover);
    color: var(--color-text-primary);
  }
  
  /* ===== MOBILE CONTENT SPACING ===== */
  .mobile-content {
    padding-bottom: calc(80px + env(safe-area-inset-bottom)); /* Account for mobile nav */
  }
  
  /* ===== MOBILE ANIMATIONS ===== */
  .mobile-slide-up {
    animation: mobile-slide-up var(--duration-300) var(--ease-out);
  }
  
  @keyframes mobile-slide-up {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  .mobile-fade-in {
    animation: mobile-fade-in var(--duration-200) var(--ease-out);
  }
  
  @keyframes mobile-fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}

/* ===== MOBILE LANDSCAPE ADJUSTMENTS ===== */
@media (max-width: 767px) and (orientation: landscape) {
  .mobile-header {
    padding-top: var(--space-2);
    min-height: 50px;
  }
  
  .mobile-nav {
    padding: var(--space-1) var(--space-4);
    padding-bottom: calc(var(--space-1) + env(safe-area-inset-bottom));
  }
  
  .mobile-nav-item {
    padding: var(--space-1);
    min-height: 40px;
  }
  
  .mobile-content {
    padding-bottom: calc(60px + env(safe-area-inset-bottom));
  }
}

/* ===== SMALL MOBILE DEVICES ===== */
@media (max-width: 374px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  .card-header,
  .card-content,
  .modal-header,
  .modal-body {
    padding: var(--space-3);
  }
  
  .card-footer,
  .modal-footer {
    padding: var(--space-2) var(--space-3);
  }
  
  .btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
  }
  
  .text-2xl { font-size: var(--font-size-xl); }
  .text-xl { font-size: var(--font-size-lg); }
  .text-lg { font-size: var(--font-size-base); }
}