/* 
 * Desktop Styles - Optimized
 * Desktop-specific responsive design optimizations (1024px and up)
 */

@media (min-width: 1024px) {
  
  /* ===== LAYOUT ADJUSTMENTS ===== */
  .container {
    padding: 0 var(--space-8);
  }
  
  /* ===== GRID ADJUSTMENTS ===== */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
  
  /* ===== COMPONENT ADJUSTMENTS ===== */
  
  /* Cards */
  .card:hover {
    transform: translateY(-6px);
  }
  
  .card-header {
    padding: var(--space-6);
  }
  
  .card-content {
    padding: var(--space-6);
  }
  
  .card-footer {
    padding: var(--space-5) var(--space-6);
  }
  
  /* Navigation */
  .nav {
    padding: var(--space-3);
    gap: var(--space-3);
  }
  
  .nav-item {
    padding: var(--space-3) var(--space-5);
  }
  
  /* Buttons */
  .btn:hover {
    transform: translateY(-2px);
  }
  
  /* Modals */
  .modal {
    max-width: 800px;
  }
  
  .modal-header {
    padding: var(--space-6);
  }
  
  .modal-body {
    padding: var(--space-6);
  }
  
  .modal-footer {
    padding: var(--space-6);
  }
  
  /* Dropdowns */
  .dropdown-menu {
    min-width: 16rem;
  }
  
  /* ===== DESKTOP-SPECIFIC UTILITIES ===== */
  .desktop-hidden {
    display: none;
  }
  
  .desktop-block {
    display: block;
  }
  
  .desktop-flex {
    display: flex;
  }
  
  .desktop-grid {
    display: grid;
  }
  
  .desktop-flex-row {
    flex-direction: row;
  }
  
  .desktop-flex-col {
    flex-direction: column;
  }
  
  .desktop-items-center {
    align-items: center;
  }
  
  .desktop-justify-between {
    justify-content: space-between;
  }
  
  .desktop-justify-center {
    justify-content: center;
  }
  
  .desktop-text-center {
    text-align: center;
  }
  
  .desktop-text-left {
    text-align: left;
  }
  
  /* ===== DESKTOP LAYOUT PATTERNS ===== */
  .desktop-sidebar-layout {
    display: grid;
    grid-template-columns: 320px 1fr;
    gap: var(--space-8);
    min-height: 100vh;
  }
  
  .desktop-two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
  }
  
  .desktop-three-column {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-6);
  }
  
  .desktop-four-column {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-6);
  }
  
  .desktop-asymmetric-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-8);
  }
  
  /* ===== DESKTOP NAVIGATION ===== */
  .desktop-nav-horizontal {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    padding: var(--space-5) var(--space-8);
    background-color: var(--color-surface);
    border-bottom: var(--border-width-1) solid var(--color-border);
    position: sticky;
    top: 0;
    z-index: var(--z-30);
    backdrop-filter: blur(10px);
  }
  
  .desktop-nav-item {
    padding: var(--space-3) var(--space-5);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
    text-decoration: none;
    transition: all var(--duration-200) var(--ease-out);
    position: relative;
  }
  
  .desktop-nav-item:hover {
    background-color: var(--color-surface-hover);
    color: var(--color-text-primary);
    transform: translateY(-1px);
  }
  
  .desktop-nav-item.active {
    background-color: var(--color-primary);
    color: white;
  }
  
  .desktop-nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background-color: var(--color-primary);
    border-radius: var(--radius-full);
  }
  
  /* ===== DESKTOP SIDEBAR ===== */
  .desktop-sidebar {
    padding: var(--space-8);
    background-color: var(--color-surface);
    border-right: var(--border-width-1) solid var(--color-border);
    height: 100vh;
    overflow-y: auto;
    position: sticky;
    top: 0;
  }
  
  .desktop-sidebar-section {
    margin-bottom: var(--space-8);
  }
  
  .desktop-sidebar-section:last-child {
    margin-bottom: 0;
  }
  
  .desktop-sidebar-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-secondary);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
    margin-bottom: var(--space-4);
  }
  
  .desktop-sidebar-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    color: var(--color-text-secondary);
    text-decoration: none;
    transition: all var(--duration-200) var(--ease-out);
    margin-bottom: var(--space-1);
  }
  
  .desktop-sidebar-item:hover {
    background-color: var(--color-surface-hover);
    color: var(--color-text-primary);
    transform: translateX(4px);
  }
  
  .desktop-sidebar-item.active {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--color-primary);
    border-left: 3px solid var(--color-primary);
    padding-left: calc(var(--space-4) - 3px);
  }
  
  .desktop-sidebar-icon {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }
  
  /* ===== DESKTOP HEADER ===== */
  .desktop-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-6) var(--space-8);
    background-color: var(--color-surface);
    border-bottom: var(--border-width-1) solid var(--color-border);
    position: sticky;
    top: 0;
    z-index: var(--z-30);
    backdrop-filter: blur(10px);
  }
  
  .desktop-header-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
  }
  
  .desktop-header-subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-top: var(--space-1);
  }
  
  .desktop-header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
  }
  
  /* ===== DESKTOP CONTENT AREAS ===== */
  .desktop-main-content {
    padding: var(--space-8);
    max-width: none;
    min-height: calc(100vh - 120px);
  }
  
  .desktop-content-section {
    margin-bottom: var(--space-12);
  }
  
  .desktop-content-section:last-child {
    margin-bottom: 0;
  }
  
  .desktop-content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-6);
  }
  
  .desktop-content-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
  }
  
  .desktop-content-description {
    font-size: var(--font-size-base);
    color: var(--color-text-secondary);
    margin-top: var(--space-2);
  }
  
  /* ===== DESKTOP CARD LAYOUTS ===== */
  .desktop-card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-6);
  }
  
  .desktop-card-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
  }
  
  .desktop-card-masonry {
    columns: 3;
    column-gap: var(--space-6);
    column-fill: balance;
  }
  
  .desktop-card-masonry .card {
    break-inside: avoid;
    margin-bottom: var(--space-5);
  }
  
  /* ===== DESKTOP FORM LAYOUTS ===== */
  .desktop-form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-6);
  }
  
  .desktop-form-two-column {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
  
  .desktop-form-full {
    grid-column: 1 / -1;
  }
  
  .desktop-form-half {
    grid-column: span 1;
  }
  
  .desktop-form-two-thirds {
    grid-column: span 2;
  }
  
  /* ===== DESKTOP MODAL ADJUSTMENTS ===== */
  .desktop-modal-large {
    max-width: 900px;
  }
  
  .desktop-modal-extra-large {
    max-width: 1200px;
  }
  
  .desktop-modal-fullscreen {
    width: calc(100vw - 8rem);
    height: calc(100vh - 8rem);
    max-width: none;
    max-height: none;
  }
  
  /* ===== DESKTOP HOVER EFFECTS ===== */
  .desktop-hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
  }
  
  .desktop-hover-scale:hover {
    transform: scale(1.02);
  }
  
  .desktop-hover-glow:hover {
    box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
  }
  
  /* ===== DESKTOP ANIMATIONS ===== */
  .desktop-slide-in-left {
    animation: desktop-slide-in-left var(--duration-500) var(--ease-out);
  }
  
  @keyframes desktop-slide-in-left {
    from {
      transform: translateX(-50px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .desktop-slide-in-right {
    animation: desktop-slide-in-right var(--duration-500) var(--ease-out);
  }
  
  @keyframes desktop-slide-in-right {
    from {
      transform: translateX(50px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .desktop-fade-in-up {
    animation: desktop-fade-in-up var(--duration-600) var(--ease-out);
  }
  
  @keyframes desktop-fade-in-up {
    from {
      transform: translateY(30px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  .desktop-stagger-children > * {
    animation: desktop-fade-in-up var(--duration-600) var(--ease-out);
  }
  
  .desktop-stagger-children > *:nth-child(1) { animation-delay: 0ms; }
  .desktop-stagger-children > *:nth-child(2) { animation-delay: 100ms; }
  .desktop-stagger-children > *:nth-child(3) { animation-delay: 200ms; }
  .desktop-stagger-children > *:nth-child(4) { animation-delay: 300ms; }
  .desktop-stagger-children > *:nth-child(5) { animation-delay: 400ms; }
  .desktop-stagger-children > *:nth-child(6) { animation-delay: 500ms; }
  
  /* ===== DESKTOP SCROLL EFFECTS ===== */
  .desktop-smooth-scroll {
    scroll-behavior: smooth;
  }
  
  .desktop-scroll-snap {
    scroll-snap-type: y mandatory;
  }
  
  .desktop-scroll-snap-item {
    scroll-snap-align: start;
  }
  
  /* ===== DESKTOP FOCUS IMPROVEMENTS ===== */
  .desktop-focus-ring:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px var(--focus-ring-color);
    border-radius: var(--radius-lg);
  }
  
  /* ===== DESKTOP TYPOGRAPHY ENHANCEMENTS ===== */
  .desktop-text-balance {
    text-wrap: balance;
  }
  
  .desktop-text-pretty {
    text-wrap: pretty;
  }
}

/* ===== LARGE DESKTOP SPECIFIC (1440px+) ===== */
@media (min-width: 1440px) {
  .container {
    padding: 0 var(--space-12);
  }
  
  .desktop-sidebar-layout {
    grid-template-columns: 360px 1fr;
    gap: var(--space-12);
  }
  
  .desktop-main-content {
    padding: var(--space-12);
  }
  
  .desktop-card-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-8);
  }
  
  .desktop-card-masonry {
    columns: 4;
    column-gap: var(--space-8);
  }
  
  .desktop-form-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-8);
  }
}

/* ===== ULTRA-WIDE DESKTOP (1920px+) ===== */
@media (min-width: 1920px) {
  .desktop-card-grid {
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  }
  
  .desktop-card-masonry {
    columns: 5;
  }
  
  .desktop-form-grid {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .desktop-ultra-wide-layout {
    display: grid;
    grid-template-columns: 400px 1fr 300px;
    gap: var(--space-12);
  }
}