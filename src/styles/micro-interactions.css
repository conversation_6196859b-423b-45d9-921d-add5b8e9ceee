/* 
 * Micro-Interactions for Notely
 * Subtle animations and interactions to enhance user experience
 */

/* ===== BUTTON MICRO-INTERACTIONS ===== */

/* Button press effect */
.btn {
  position: relative;
  overflow: hidden;
  transition: transform var(--duration-200) var(--ease-out),
              background-color var(--duration-200) var(--ease-out),
              box-shadow var(--duration-200) var(--ease-out),
              color var(--duration-200) var(--ease-out);
}

.btn:active {
  transform: scale(0.97);
}

/* Button ripple effect */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple .ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Button hover lift effect */
.btn-lift {
  transition: transform var(--duration-200) var(--ease-out),
              box-shadow var(--duration-200) var(--ease-out);
}

.btn-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-lift:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Button loading state */
.btn-loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1rem;
  height: 1rem;
  margin-top: -0.5rem;
  margin-left: -0.5rem;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  animation: spin 0.75s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Button success state */
.btn-success-animation {
  position: relative;
  color: transparent !important;
  pointer-events: none;
  background-color: var(--color-success) !important;
  border-color: var(--color-success) !important;
}

.btn-success-animation::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1rem;
  height: 0.5rem;
  margin-top: -0.25rem;
  margin-left: -0.5rem;
  border-left: 2px solid white;
  border-bottom: 2px solid white;
  transform: rotate(-45deg);
  animation: checkmark 0.3s ease-out forwards;
}

@keyframes checkmark {
  0% {
    opacity: 0;
    transform: rotate(-45deg) scale(0);
  }
  100% {
    opacity: 1;
    transform: rotate(-45deg) scale(1);
  }
}

/* ===== CARD MICRO-INTERACTIONS ===== */

/* Card hover lift effect */
.card-lift {
  transition: transform var(--duration-300) var(--ease-out),
              box-shadow var(--duration-300) var(--ease-out);
}

.card-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Card hover glow effect */
.card-glow {
  transition: box-shadow var(--duration-300) var(--ease-out);
}

.card-glow:hover {
  box-shadow: 0 0 20px rgba(79, 70, 229, 0.4);
}

/* Card content reveal on hover */
.card-reveal {
  position: relative;
  overflow: hidden;
}

.card-reveal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  transform: translateY(100%);
  transition: transform var(--duration-300) var(--ease-out);
}

.card-reveal:hover .card-reveal-content {
  transform: translateY(0);
}

/* Card image zoom on hover */
.card-image-zoom {
  overflow: hidden;
}

.card-image-zoom img {
  transition: transform var(--duration-500) var(--ease-out);
}

.card-image-zoom:hover img {
  transform: scale(1.05);
}

/* ===== FORM MICRO-INTERACTIONS ===== */

/* Input focus effect */
.form-input-focus {
  transition: border-color var(--duration-200) var(--ease-out),
              box-shadow var(--duration-200) var(--ease-out);
}

.form-input-focus:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Checkbox animation */
.checkbox-animation {
  position: relative;
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--color-border);
  border-radius: 0.25rem;
  background-color: var(--color-surface);
  cursor: pointer;
  transition: background-color var(--duration-200) var(--ease-out),
              border-color var(--duration-200) var(--ease-out);
}

.checkbox-animation:checked {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-animation:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.5rem;
  height: 0.25rem;
  border-left: 2px solid white;
  border-bottom: 2px solid white;
  transform: translate(-50%, -70%) rotate(-45deg);
  animation: checkbox-check 0.2s ease-out forwards;
}

@keyframes checkbox-check {
  0% {
    opacity: 0;
    transform: translate(-50%, -70%) rotate(-45deg) scale(0);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -70%) rotate(-45deg) scale(1);
  }
}

/* Toggle switch animation */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.5rem;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-border);
  border-radius: 1.5rem;
  transition: background-color var(--duration-200) var(--ease-out);
}

.toggle-slider::before {
  position: absolute;
  content: '';
  height: 1.25rem;
  width: 1.25rem;
  left: 0.125rem;
  bottom: 0.125rem;
  background-color: white;
  border-radius: 50%;
  transition: transform var(--duration-200) var(--ease-out),
              background-color var(--duration-200) var(--ease-out);
}

.toggle-switch input:checked + .toggle-slider {
  background-color: var(--color-primary);
}

.toggle-switch input:checked + .toggle-slider::before {
  transform: translateX(1.5rem);
}

/* ===== NAVIGATION MICRO-INTERACTIONS ===== */

/* Tab hover effect */
.tab-hover {
  position: relative;
  transition: color var(--duration-200) var(--ease-out);
}

.tab-hover::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary);
  transform: scaleX(0);
  transform-origin: center;
  transition: transform var(--duration-200) var(--ease-out);
}

.tab-hover:hover {
  color: var(--color-primary);
}

.tab-hover:hover::after {
  transform: scaleX(1);
}

.tab-hover.active::after {
  transform: scaleX(1);
}

/* Menu item hover effect */
.menu-item-hover {
  position: relative;
  transition: color var(--duration-200) var(--ease-out),
              background-color var(--duration-200) var(--ease-out);
}

.menu-item-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0.25rem;
  height: 0;
  background-color: var(--color-primary);
  transition: height var(--duration-200) var(--ease-out);
}

.menu-item-hover:hover {
  background-color: var(--color-surface-hover);
}

.menu-item-hover:hover::before {
  height: 100%;
}

.menu-item-hover.active::before {
  height: 100%;
}

/* Dropdown animation */
.dropdown-animation {
  position: relative;
}

.dropdown-menu-animation {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: opacity var(--duration-200) var(--ease-out),
              transform var(--duration-200) var(--ease-out),
              visibility var(--duration-200) var(--ease-out);
}

.dropdown-animation.open .dropdown-menu-animation {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* ===== NOTIFICATION MICRO-INTERACTIONS ===== */

/* Toast notification animation */
.toast-animation {
  transform: translateX(100%);
  opacity: 0;
  transition: transform var(--duration-300) var(--ease-out),
              opacity var(--duration-300) var(--ease-out);
}

.toast-animation.show {
  transform: translateX(0);
  opacity: 1;
}

/* Badge pulse animation */
.badge-pulse {
  position: relative;
}

.badge-pulse::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--color-error);
  border-radius: 50%;
  animation: badge-pulse 1.5s infinite;
}

@keyframes badge-pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}

/* Alert animation */
.alert-animation {
  animation: alert-slide-in 0.3s var(--ease-out) forwards;
}

@keyframes alert-slide-in {
  0% {
    opacity: 0;
    transform: translateY(-1rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== LOADING MICRO-INTERACTIONS ===== */

/* Skeleton loading animation */
.skeleton-loading {
  background: linear-gradient(90deg, 
              var(--color-surface-hover) 25%, 
              var(--color-surface) 50%, 
              var(--color-surface-hover) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Dots loading animation */
.dots-loading::after {
  content: '.';
  animation: dots-loading 1.5s steps(4, end) infinite;
}

@keyframes dots-loading {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60% {
    content: '...';
  }
  80%, 100% {
    content: '';
  }
}

/* Spinner loading animation */
.spinner-loading {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--color-primary);
  animation: spinner-loading 0.75s linear infinite;
}

@keyframes spinner-loading {
  to {
    transform: rotate(360deg);
  }
}

/* Progress bar animation */
.progress-bar {
  height: 0.25rem;
  background-color: var(--color-border);
  border-radius: 0.125rem;
  overflow: hidden;
}

.progress-bar-value {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 0.125rem;
  transition: width var(--duration-300) var(--ease-out);
}

.progress-bar-indeterminate .progress-bar-value {
  width: 50% !important;
  animation: progress-bar-indeterminate 1.5s infinite;
}

@keyframes progress-bar-indeterminate {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .btn,
  .btn-ripple .ripple,
  .btn-lift,
  .btn-loading::after,
  .btn-success-animation::after,
  .card-lift,
  .card-glow,
  .card-reveal-content,
  .card-image-zoom img,
  .form-input-focus,
  .checkbox-animation,
  .checkbox-animation:checked::after,
  .toggle-slider,
  .toggle-slider::before,
  .tab-hover,
  .tab-hover::after,
  .menu-item-hover,
  .menu-item-hover::before,
  .dropdown-menu-animation,
  .toast-animation,
  .badge-pulse::after,
  .alert-animation,
  .skeleton-loading,
  .dots-loading::after,
  .spinner-loading,
  .progress-bar-value,
  .progress-bar-indeterminate .progress-bar-value {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
}