/* 
 * Base Styles - Optimized
 * Consolidated base styles with performance optimizations
 */

/* Reset and normalize */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: var(--font-sans);
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  min-height: 100vh;
  transition: background-color var(--duration-300) var(--ease-out),
              color var(--duration-300) var(--ease-out);
}

/* Focus management */
:focus-visible {
  outline: 2px solid var(--focus-ring-color);
  outline-offset: 2px;
}

/* Remove default focus for mouse users */
:focus:not(:focus-visible) {
  outline: none;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-hover);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-tertiary);
}

/* Selection styling */
::selection {
  background-color: rgba(79, 70, 229, 0.2);
  color: var(--color-text-primary);
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Link styling */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-200) var(--ease-out);
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Button reset */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}

/* Form element reset */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  color: inherit;
}

/* List reset */
ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* Heading reset */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

/* Paragraph reset */
p {
  margin: 0;
  line-height: var(--line-height-relaxed);
}

/* Table reset */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 1000;
  transition: top var(--duration-200) var(--ease-out);
}

.skip-link:focus {
  top: 6px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode */
@media (forced-colors: active) {
  :focus-visible {
    outline: 2px solid Highlight;
  }
  
  a {
    color: LinkText;
  }
  
  a:hover {
    color: LinkText;
  }
}