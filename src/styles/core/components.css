/* 
 * Component Styles - Optimized
 * Consolidated component styles with performance optimizations
 */

/* ===== CARD COMPONENT ===== */
.card {
  background-color: var(--color-surface);
  border: var(--border-width-1) solid var(--color-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform var(--duration-300) var(--ease-out),
              box-shadow var(--duration-300) var(--ease-out),
              border-color var(--duration-300) var(--ease-out);
  will-change: transform;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-light);
}

.card-header {
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  border-bottom: 1px solid var(--color-border);
}

.card-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  object-fit: cover;
  border: 2px solid var(--color-surface);
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
}

.card-content {
  padding: var(--space-6);
  flex: 1;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-2);
  line-height: var(--line-height-tight);
}

.card-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4);
}

.card-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--color-surface-hover);
}

/* Platform indicators */
.card-platform-twitter { border-top: 4px solid var(--color-twitter); }
.card-platform-linkedin { border-top: 4px solid var(--color-linkedin); }
.card-platform-reddit { border-top: 4px solid var(--color-reddit); }
.card-platform-instagram { border-top: 4px solid var(--color-instagram); }
.card-platform-pinterest { border-top: 4px solid var(--color-pinterest); }
.card-platform-web { border-top: 4px solid var(--color-web); }

/* ===== BUTTON COMPONENT ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-none);
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  border: var(--border-width-1) solid transparent;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px var(--focus-ring-color);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button variants */
.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-text-primary);
  border-color: transparent;
}

.btn-ghost:hover {
  background-color: var(--color-surface-hover);
}

/* Button sizes */
.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-base);
  border-radius: var(--radius-xl);
}

.btn-icon {
  padding: var(--space-3);
  border-radius: var(--radius-lg);
}

.btn-icon.btn-sm {
  padding: var(--space-2);
  border-radius: var(--radius-md);
}

.btn-icon.btn-lg {
  padding: var(--space-4);
  border-radius: var(--radius-xl);
}

/* ===== FORM COMPONENTS ===== */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-2);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: var(--border-width-1) solid var(--color-border);
  border-radius: var(--radius-lg);
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: border-color var(--duration-200) var(--ease-out),
              box-shadow var(--duration-200) var(--ease-out);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--focus-ring-color);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--color-text-tertiary);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-error {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--space-1);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: var(--color-error);
}

/* ===== BADGE COMPONENT ===== */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-none);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.badge-primary {
  background-color: var(--color-primary);
  color: white;
}

.badge-success {
  background-color: var(--color-success);
  color: white;
}

.badge-warning {
  background-color: var(--color-warning);
  color: white;
}

.badge-error {
  background-color: var(--color-error);
  color: white;
}

.badge-secondary {
  background-color: var(--color-surface-hover);
  color: var(--color-text-secondary);
  border: var(--border-width-1) solid var(--color-border);
}

/* ===== NAVIGATION COMPONENT ===== */
.nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2);
  background-color: var(--color-surface);
  border-radius: var(--radius-xl);
  border: var(--border-width-1) solid var(--color-border);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
}

.nav-item:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-text-primary);
}

.nav-item.active {
  background-color: var(--color-primary);
  color: white;
}

.nav-item-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* ===== MODAL COMPONENT ===== */
.modal-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-40);
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--duration-300) var(--ease-out),
              visibility var(--duration-300) var(--ease-out);
}

.modal-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background-color: var(--color-surface);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  z-index: var(--z-50);
  width: 100%;
  max-width: 32rem;
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--duration-300) var(--ease-out),
              transform var(--duration-300) var(--ease-out),
              visibility var(--duration-300) var(--ease-out);
}

.modal.show {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) scale(1);
}

.modal-header {
  padding: var(--space-6);
  border-bottom: var(--border-width-1) solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.modal-close {
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  color: var(--color-text-secondary);
  transition: all var(--duration-200) var(--ease-out);
}

.modal-close:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-text-primary);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: var(--border-width-1) solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-3);
}

/* ===== DROPDOWN COMPONENT ===== */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: var(--z-10);
  min-width: 12rem;
  padding: var(--space-2);
  margin-top: var(--space-2);
  background-color: var(--color-surface);
  border: var(--border-width-1) solid var(--color-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: opacity var(--duration-200) var(--ease-out),
              transform var(--duration-200) var(--ease-out),
              visibility var(--duration-200) var(--ease-out);
}

.dropdown.open .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: background-color var(--duration-200) var(--ease-out);
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: var(--color-surface-hover);
}

.dropdown-divider {
  height: var(--border-width-1);
  margin: var(--space-2) 0;
  background-color: var(--color-border);
}

/* ===== TOAST COMPONENT ===== */
.toast-container {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  z-index: var(--z-50);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.toast {
  background-color: var(--color-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-4) var(--space-5);
  min-width: 20rem;
  max-width: 24rem;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transform: translateX(100%);
  opacity: 0;
  transition: transform var(--duration-300) var(--ease-out),
              opacity var(--duration-300) var(--ease-out);
}

.toast.show {
  transform: translateX(0);
  opacity: 1;
}

.toast-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.toast-content {
  flex-grow: 1;
}

.toast-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.toast-message {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.toast-close {
  flex-shrink: 0;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  transition: all var(--duration-200) var(--ease-out);
}

.toast-close:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-text-primary);
}

/* Toast variants */
.toast-success { border-left: 4px solid var(--color-success); }
.toast-warning { border-left: 4px solid var(--color-warning); }
.toast-error { border-left: 4px solid var(--color-error); }
.toast-info { border-left: 4px solid var(--color-info); }

/* ===== LOADING STATES ===== */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--color-surface-hover) 25%,
    var(--color-surface) 50%,
    var(--color-surface-hover) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.skeleton-text {
  height: 1rem;
  margin-bottom: var(--space-2);
}

.skeleton-circle {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-full);
}

.skeleton-card {
  height: 12rem;
  border-radius: var(--radius-xl);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
  .card-header {
    padding: var(--space-4);
  }
  
  .card-content {
    padding: var(--space-4);
  }
  
  .card-footer {
    padding: var(--space-3) var(--space-4);
  }
  
  .card-avatar {
    width: 40px;
    height: 40px;
  }
  
  .modal {
    max-width: calc(100vw - 2rem);
    margin: 1rem;
  }
  
  .toast-container {
    bottom: var(--space-4);
    right: var(--space-4);
    left: var(--space-4);
  }
  
  .toast {
    min-width: auto;
    max-width: none;
  }
}