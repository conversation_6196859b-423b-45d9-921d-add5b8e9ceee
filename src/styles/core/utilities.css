/* 
 * Utility Classes - Optimized
 * Performance-optimized utility classes for common patterns
 */

/* ===== TEXT UTILITIES ===== */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.leading-none { line-height: var(--line-height-none); }
.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* Text truncation utilities */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}

/* ===== SPACING UTILITIES ===== */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--space-1); margin-right: var(--space-1); }
.mx-2 { margin-left: var(--space-2); margin-right: var(--space-2); }
.mx-3 { margin-left: var(--space-3); margin-right: var(--space-3); }
.mx-4 { margin-left: var(--space-4); margin-right: var(--space-4); }
.mx-auto { margin-left: auto; margin-right: auto; }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--space-1); margin-bottom: var(--space-1); }
.my-2 { margin-top: var(--space-2); margin-bottom: var(--space-2); }
.my-3 { margin-top: var(--space-3); margin-bottom: var(--space-3); }
.my-4 { margin-top: var(--space-4); margin-bottom: var(--space-4); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--space-1); }
.ml-2 { margin-left: var(--space-2); }
.ml-3 { margin-left: var(--space-3); }
.ml-4 { margin-left: var(--space-4); }
.ml-auto { margin-left: auto; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--space-1); }
.mr-2 { margin-right: var(--space-2); }
.mr-3 { margin-right: var(--space-3); }
.mr-4 { margin-right: var(--space-4); }
.mr-auto { margin-right: auto; }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }

/* ===== BACKGROUND UTILITIES ===== */
.bg-transparent { background-color: transparent; }
.bg-surface { background-color: var(--color-surface); }
.bg-surface-hover { background-color: var(--color-surface-hover); }
.bg-primary { background-color: var(--color-primary); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }
.bg-info { background-color: var(--color-info); }

/* ===== BORDER UTILITIES ===== */
.border { border: var(--border-width-1) solid var(--color-border); }
.border-0 { border: 0; }
.border-t { border-top: var(--border-width-1) solid var(--color-border); }
.border-b { border-bottom: var(--border-width-1) solid var(--color-border); }
.border-l { border-left: var(--border-width-1) solid var(--color-border); }
.border-r { border-right: var(--border-width-1) solid var(--color-border); }

.border-primary { border-color: var(--color-primary); }
.border-success { border-color: var(--color-success); }
.border-warning { border-color: var(--color-warning); }
.border-error { border-color: var(--color-error); }
.border-info { border-color: var(--color-info); }

.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-full { border-radius: var(--radius-full); }

/* ===== SHADOW UTILITIES ===== */
.shadow-none { box-shadow: none; }
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* ===== TRANSITION UTILITIES ===== */
.transition-none { transition: none; }
.transition-all { 
  transition: all var(--duration-200) var(--ease-out); 
}
.transition-colors { 
  transition: color var(--duration-200) var(--ease-out),
              background-color var(--duration-200) var(--ease-out),
              border-color var(--duration-200) var(--ease-out); 
}
.transition-opacity { 
  transition: opacity var(--duration-200) var(--ease-out); 
}
.transition-transform { 
  transition: transform var(--duration-200) var(--ease-out); 
}

.duration-75 { transition-duration: var(--duration-75); }
.duration-100 { transition-duration: var(--duration-100); }
.duration-150 { transition-duration: var(--duration-150); }
.duration-200 { transition-duration: var(--duration-200); }
.duration-300 { transition-duration: var(--duration-300); }
.duration-500 { transition-duration: var(--duration-500); }

.ease-linear { transition-timing-function: var(--ease-linear); }
.ease-in { transition-timing-function: var(--ease-in); }
.ease-out { transition-timing-function: var(--ease-out); }
.ease-in-out { transition-timing-function: var(--ease-in-out); }

/* ===== TRANSFORM UTILITIES ===== */
.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

.translate-x-0 { --tw-translate-x: 0px; }
.translate-x-1 { --tw-translate-x: var(--space-1); }
.translate-x-2 { --tw-translate-x: var(--space-2); }
.translate-x-4 { --tw-translate-x: var(--space-4); }
.-translate-x-1 { --tw-translate-x: calc(-1 * var(--space-1)); }
.-translate-x-2 { --tw-translate-x: calc(-1 * var(--space-2)); }
.-translate-x-4 { --tw-translate-x: calc(-1 * var(--space-4)); }

.translate-y-0 { --tw-translate-y: 0px; }
.translate-y-1 { --tw-translate-y: var(--space-1); }
.translate-y-2 { --tw-translate-y: var(--space-2); }
.translate-y-4 { --tw-translate-y: var(--space-4); }
.-translate-y-1 { --tw-translate-y: calc(-1 * var(--space-1)); }
.-translate-y-2 { --tw-translate-y: calc(-1 * var(--space-2)); }
.-translate-y-4 { --tw-translate-y: calc(-1 * var(--space-4)); }

.scale-0 { --tw-scale-x: 0; --tw-scale-y: 0; }
.scale-50 { --tw-scale-x: 0.5; --tw-scale-y: 0.5; }
.scale-75 { --tw-scale-x: 0.75; --tw-scale-y: 0.75; }
.scale-90 { --tw-scale-x: 0.9; --tw-scale-y: 0.9; }
.scale-95 { --tw-scale-x: 0.95; --tw-scale-y: 0.95; }
.scale-100 { --tw-scale-x: 1; --tw-scale-y: 1; }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }
.scale-110 { --tw-scale-x: 1.1; --tw-scale-y: 1.1; }
.scale-125 { --tw-scale-x: 1.25; --tw-scale-y: 1.25; }

.rotate-0 { --tw-rotate: 0deg; }
.rotate-45 { --tw-rotate: 45deg; }
.rotate-90 { --tw-rotate: 90deg; }
.rotate-180 { --tw-rotate: 180deg; }
.-rotate-45 { --tw-rotate: -45deg; }
.-rotate-90 { --tw-rotate: -90deg; }
.-rotate-180 { --tw-rotate: -180deg; }

/* ===== OPACITY UTILITIES ===== */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* ===== CURSOR UTILITIES ===== */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

/* ===== INTERACTION UTILITIES ===== */
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* ===== HOVER UTILITIES ===== */
.hover\:bg-surface-hover:hover { background-color: var(--color-surface-hover); }
.hover\:bg-primary:hover { background-color: var(--color-primary); }
.hover\:text-primary:hover { color: var(--color-text-primary); }
.hover\:text-secondary:hover { color: var(--color-text-secondary); }
.hover\:border-primary:hover { border-color: var(--color-primary); }
.hover\:shadow-md:hover { box-shadow: var(--shadow-md); }
.hover\:shadow-lg:hover { box-shadow: var(--shadow-lg); }
.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }
.hover\:-translate-y-1:hover { --tw-translate-y: calc(-1 * var(--space-1)); }
.hover\:-translate-y-2:hover { --tw-translate-y: calc(-1 * var(--space-2)); }

/* ===== FOCUS UTILITIES ===== */
.focus\:outline-none:focus { outline: none; }
.focus\:ring:focus { box-shadow: 0 0 0 3px var(--focus-ring-color); }
.focus\:ring-primary:focus { box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.4); }
.focus\:border-primary:focus { border-color: var(--color-primary); }

/* ===== ACTIVE UTILITIES ===== */
.active\:scale-95:active { --tw-scale-x: 0.95; --tw-scale-y: 0.95; }
.active\:translate-y-0:active { --tw-translate-y: 0px; }

/* ===== RESPONSIVE UTILITIES ===== */
@media (min-width: 640px) {
  .sm\:text-sm { font-size: var(--font-size-sm); }
  .sm\:text-base { font-size: var(--font-size-base); }
  .sm\:text-lg { font-size: var(--font-size-lg); }
  .sm\:text-xl { font-size: var(--font-size-xl); }
  
  .sm\:p-4 { padding: var(--space-4); }
  .sm\:p-6 { padding: var(--space-6); }
  .sm\:px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
  .sm\:py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
}

@media (min-width: 768px) {
  .md\:text-base { font-size: var(--font-size-base); }
  .md\:text-lg { font-size: var(--font-size-lg); }
  .md\:text-xl { font-size: var(--font-size-xl); }
  .md\:text-2xl { font-size: var(--font-size-2xl); }
  
  .md\:p-6 { padding: var(--space-6); }
  .md\:p-8 { padding: var(--space-8); }
  .md\:px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }
  .md\:py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
}

@media (min-width: 1024px) {
  .lg\:text-lg { font-size: var(--font-size-lg); }
  .lg\:text-xl { font-size: var(--font-size-xl); }
  .lg\:text-2xl { font-size: var(--font-size-2xl); }
  .lg\:text-3xl { font-size: var(--font-size-3xl); }
  
  .lg\:p-8 { padding: var(--space-8); }
  .lg\:px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }
  .lg\:py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }
}

/* ===== PRINT UTILITIES ===== */
@media print {
  .print\:hidden { display: none !important; }
  .print\:block { display: block !important; }
  .print\:text-black { color: black !important; }
  .print\:bg-white { background-color: white !important; }
}