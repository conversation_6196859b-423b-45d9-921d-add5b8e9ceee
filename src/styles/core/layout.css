/* 
 * Layout Styles - Optimized
 * Consolidated layout utilities and grid systems
 */

/* Container system */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-2xl {
  max-width: 1536px;
}

/* Grid system */
.grid {
  display: grid;
  gap: var(--space-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }

/* Auto-fit grid for responsive cards */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* Flexbox utilities */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-initial {
  flex: 0 1 auto;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

/* Spacing utilities */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.gap-x-1 { column-gap: var(--space-1); }
.gap-x-2 { column-gap: var(--space-2); }
.gap-x-3 { column-gap: var(--space-3); }
.gap-x-4 { column-gap: var(--space-4); }
.gap-x-5 { column-gap: var(--space-5); }
.gap-x-6 { column-gap: var(--space-6); }

.gap-y-1 { row-gap: var(--space-1); }
.gap-y-2 { row-gap: var(--space-2); }
.gap-y-3 { row-gap: var(--space-3); }
.gap-y-4 { row-gap: var(--space-4); }
.gap-y-5 { row-gap: var(--space-5); }
.gap-y-6 { row-gap: var(--space-6); }

/* Positioning */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

.static {
  position: static;
}

/* Z-index utilities */
.z-0 { z-index: var(--z-0); }
.z-10 { z-index: var(--z-10); }
.z-20 { z-index: var(--z-20); }
.z-30 { z-index: var(--z-30); }
.z-40 { z-index: var(--z-40); }
.z-50 { z-index: var(--z-50); }

/* Overflow utilities */
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

/* Display utilities */
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.hidden {
  display: none;
}

/* Visibility utilities */
.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

/* Width and height utilities */
.w-full {
  width: 100%;
}

.w-auto {
  width: auto;
}

.h-full {
  height: 100%;
}

.h-auto {
  height: auto;
}

.min-h-screen {
  min-height: 100vh;
}

.max-w-none {
  max-width: none;
}

.max-w-full {
  max-width: 100%;
}

/* Aspect ratio utilities */
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-4-3 {
  aspect-ratio: 4 / 3;
}

.aspect-3-2 {
  aspect-ratio: 3 / 2;
}

/* Object fit utilities */
.object-contain {
  object-fit: contain;
}

.object-cover {
  object-fit: cover;
}

.object-fill {
  object-fit: fill;
}

.object-none {
  object-fit: none;
}

.object-scale-down {
  object-fit: scale-down;
}

/* Responsive breakpoints */
@media (min-width: 640px) {
  .container {
    padding: 0 var(--space-6);
  }
  
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  
  .sm\:flex-row { flex-direction: row; }
  .sm\:flex-col { flex-direction: column; }
  
  .sm\:block { display: block; }
  .sm\:hidden { display: none; }
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-8);
  }
  
  .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  
  .md\:flex-row { flex-direction: row; }
  .md\:flex-col { flex-direction: column; }
  
  .md\:block { display: block; }
  .md\:hidden { display: none; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  
  .lg\:flex-row { flex-direction: row; }
  .lg\:flex-col { flex-direction: column; }
  
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
}