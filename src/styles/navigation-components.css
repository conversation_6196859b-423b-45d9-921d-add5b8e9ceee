/* 
 * Navigation Components
 * Enhanced navigation elements with improved accessibility, visual design, and interaction states
 */

/* ===== PLATFORM TABS ===== */

.platform-tabs-container {
  display: flex;
  width: 100%;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) transparent;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-2);
  position: relative;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.platform-tabs-container::-webkit-scrollbar {
  height: 4px;
}

.platform-tabs-container::-webkit-scrollbar-track {
  background: transparent;
}

.platform-tabs-container::-webkit-scrollbar-thumb {
  background-color: var(--color-border);
  border-radius: var(--radius-full);
}

/* Fade indicators for overflow */
.platform-tabs-container::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 40px;
  background: linear-gradient(to right, transparent, var(--color-background));
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--duration-200) var(--ease-out);
}

.platform-tabs-container.has-overflow::after {
  opacity: 1;
}

.platform-tabs {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-1) 0;
  flex-wrap: nowrap;
}

.platform-tab {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background-color: var(--color-surface);
  border: var(--border-width-1) solid var(--color-border);
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

/* Platform tab hover state */
.platform-tab:hover {
  color: var(--color-text-primary);
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Platform tab active state */
.platform-tab.active {
  color: var(--color-text-on-primary);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

/* Platform tab focus state */
.platform-tab:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-background), 0 0 0 4px var(--focus-ring-color);
}

/* Platform tab pressed state */
.platform-tab:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Platform tab icon */
.platform-tab-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  transition: transform var(--duration-200) var(--ease-out);
}

.platform-tab:hover .platform-tab-icon {
  transform: scale(1.1);
}

/* Platform tab count badge */
.platform-tab-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 var(--space-1);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  background-color: var(--color-surface-hover);
  color: var(--color-text-secondary);
  margin-left: var(--space-1);
  transition: all var(--duration-200) var(--ease-out);
}

.platform-tab:hover .platform-tab-count {
  background-color: var(--color-border-hover);
  color: var(--color-text-primary);
}

.platform-tab.active .platform-tab-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--color-text-on-primary);
}

/* Platform-specific styling */
.platform-tab.all {
  border-color: var(--color-gray-400);
}

.platform-tab.all.active {
  background-color: var(--color-gray-700);
  border-color: var(--color-gray-700);
}

.platform-tab.twitter {
  border-color: var(--color-twitter);
}

.platform-tab.twitter.active {
  background-color: var(--color-twitter);
  border-color: var(--color-twitter);
}

.platform-tab.linkedin {
  border-color: var(--color-linkedin);
}

.platform-tab.linkedin.active {
  background-color: var(--color-linkedin);
  border-color: var(--color-linkedin);
}

.platform-tab.reddit {
  border-color: var(--color-reddit);
}

.platform-tab.reddit.active {
  background-color: var(--color-reddit);
  border-color: var(--color-reddit);
}

.platform-tab.instagram {
  border-color: var(--color-instagram);
}

.platform-tab.instagram.active {
  background-color: var(--color-instagram);
  border-color: var(--color-instagram);
}

.platform-tab.pinterest {
  border-color: var(--color-pinterest);
}

.platform-tab.pinterest.active {
  background-color: var(--color-pinterest);
  border-color: var(--color-pinterest);
}

.platform-tab.web {
  border-color: var(--color-web);
}

.platform-tab.web.active {
  background-color: var(--color-web);
  border-color: var(--color-web);
}

/* Ripple effect for platform tabs */
.platform-tab .ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .platform-tabs {
    gap: var(--space-2);
  }
  
  .platform-tab {
    padding: var(--space-2) var(--space-3);
  }
  
  .platform-tab-icon {
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 480px) {
  .platform-tabs-container {
    margin-bottom: var(--space-4);
  }
  
  .platform-tab {
    padding: var(--space-2) var(--space-2);
    font-size: var(--font-size-xs);
  }
  
  .platform-tab-count {
    min-width: 16px;
    height: 16px;
    font-size: 10px;
  }
}

/* Accessibility adjustments */
@media (prefers-reduced-motion: reduce) {
  .platform-tab,
  .platform-tab-icon,
  .platform-tab-count {
    transition: none;
  }
  
  .platform-tab:hover {
    transform: none;
  }
  
  .platform-tab:hover .platform-tab-icon {
    transform: none;
  }
  
  .platform-tab .ripple {
    display: none;
  }
}

/* High contrast mode adjustments */
@media (forced-colors: active) {
  .platform-tab {
    border: 2px solid ButtonText;
  }
  
  .platform-tab.active {
    border: 2px solid HighlightText;
    background-color: Highlight;
    color: HighlightText;
  }
}
/* ===== FILTER AND TAG COMPONENTS ===== */

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-right: var(--space-4);
}

.filter-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
  display: block;
}

/* Filter chip base styles */
.filter-chip {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background-color: var(--color-surface);
  border: var(--border-width-1) solid var(--color-border);
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
}

/* Filter chip hover state */
.filter-chip:hover {
  color: var(--color-text-primary);
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-xs);
}

/* Filter chip active state */
.filter-chip.active {
  color: var(--color-text-on-primary);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* Filter chip focus state */
.filter-chip:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-background), 0 0 0 4px var(--focus-ring-color);
}

/* Filter chip pressed state */
.filter-chip:active {
  transform: translateY(0);
  box-shadow: none;
}

/* Filter chip icon */
.filter-chip-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

/* Filter chip remove button */
.filter-chip-remove {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background-color: transparent;
  color: currentColor;
  margin-left: var(--space-1);
  opacity: 0.7;
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
  border: none;
  padding: 0;
}

.filter-chip-remove:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

.filter-chip.active .filter-chip-remove {
  color: var(--color-text-on-primary);
  opacity: 0.8;
}

.filter-chip.active .filter-chip-remove:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.2);
}

/* Tag styles */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background-color: var(--color-surface);
  border: var(--border-width-1) solid var(--color-border);
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
}

.tag:hover {
  color: var(--color-text-primary);
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-xs);
}

.tag.active {
  color: var(--color-text-on-primary);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.tag:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-background), 0 0 0 4px var(--focus-ring-color);
}

/* Tag container */
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin: var(--space-3) 0;
}

/* Tag with icon */
.tag-with-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.tag-icon {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

/* Tag count */
.tag-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  height: 16px;
  padding: 0 var(--space-1);
  border-radius: var(--radius-full);
  font-size: 10px;
  font-weight: var(--font-weight-medium);
  background-color: var(--color-surface-hover);
  color: var(--color-text-secondary);
  margin-left: var(--space-1);
  transition: all var(--duration-200) var(--ease-out);
}

.tag:hover .tag-count {
  background-color: var(--color-border-hover);
  color: var(--color-text-primary);
}

.tag.active .tag-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--color-text-on-primary);
}

/* Tag colors */
.tag.blue {
  border-color: var(--color-info);
  color: var(--color-info);
}

.tag.blue:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.tag.blue.active {
  background-color: var(--color-info);
  color: white;
}

.tag.green {
  border-color: var(--color-success);
  color: var(--color-success);
}

.tag.green:hover {
  background-color: rgba(16, 185, 129, 0.1);
}

.tag.green.active {
  background-color: var(--color-success);
  color: white;
}

.tag.red {
  border-color: var(--color-error);
  color: var(--color-error);
}

.tag.red:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

.tag.red.active {
  background-color: var(--color-error);
  color: white;
}

.tag.yellow {
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.tag.yellow:hover {
  background-color: rgba(245, 158, 11, 0.1);
}

.tag.yellow.active {
  background-color: var(--color-warning);
  color: white;
}

.tag.purple {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.tag.purple:hover {
  background-color: rgba(99, 102, 241, 0.1);
}

.tag.purple.active {
  background-color: var(--color-primary);
  color: white;
}

/* Tag overflow indicator */
.tag-overflow {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background-color: var(--color-surface-hover);
  border: var(--border-width-1) solid var(--color-border);
  cursor: pointer;
  user-select: none;
}

.tag-overflow:hover {
  color: var(--color-text-primary);
  background-color: var(--color-surface);
  border-color: var(--color-border-hover);
}

/* Filter section */
.filter-section {
  margin-bottom: var(--space-6);
}

.filter-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.filter-section-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.filter-section-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.filter-clear-all {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background: none;
  border: none;
  padding: var(--space-1) var(--space-2);
  cursor: pointer;
  transition: color var(--duration-200) var(--ease-out);
}

.filter-clear-all:hover {
  color: var(--color-error);
}

/* Active filters display */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
  padding: var(--space-3);
  background-color: var(--color-surface);
  border: var(--border-width-1) solid var(--color-border);
  border-radius: var(--radius-lg);
}

.active-filters-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-right: var(--space-2);
}

/* Empty state for filters */
.no-filters {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .filter-group {
    margin-right: 0;
  }
  
  .active-filters {
    padding: var(--space-2);
  }
}

/* Accessibility adjustments */
@media (prefers-reduced-motion: reduce) {
  .filter-chip,
  .tag,
  .filter-chip-remove,
  .tag-count {
    transition: none;
  }
  
  .filter-chip:hover,
  .tag:hover {
    transform: none;
  }
}

/* High contrast mode adjustments */
@media (forced-colors: active) {
  .filter-chip,
  .tag {
    border: 1px solid ButtonText;
  }
  
  .filter-chip.active,
  .tag.active {
    border: 1px solid HighlightText;
    background-color: Highlight;
    color: HighlightText;
  }
  
  .filter-chip-remove {
    border: 1px solid currentColor;
  }
}