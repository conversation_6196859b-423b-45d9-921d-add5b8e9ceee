# Component Usage Guide

## Overview

This guide provides detailed examples and best practices for using each component in the Notely Design System. Each component includes code examples, variations, and implementation guidelines.

## Table of Contents

1. [Card Components](#card-components)
2. [Button Components](#button-components)
3. [Form Components](#form-components)
4. [Navigation Components](#navigation-components)
5. [Modal Components](#modal-components)
6. [Badge Components](#badge-components)
7. [Alert Components](#alert-components)
8. [Loading Components](#loading-components)
9. [Platform Components](#platform-components)
10. [Layout Components](#layout-components)

---

## Card Components

### Basic Card

The most fundamental card component for displaying content.

```html
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Basic Card Title</h3>
  </div>
  <div class="card-content">
    <p class="card-text">This is the main content of the card. It can contain text, images, or other elements.</p>
  </div>
  <div class="card-footer">
    <div class="metadata-row">
      <span class="metadata-item">
        <svg class="metadata-item-icon">...</svg>
        <span class="metadata-item-count">42</span>
      </span>
    </div>
    <div class="action-buttons">
      <button class="action-button">
        <svg>...</svg>
      </button>
    </div>
  </div>
</div>
```

### Social Media Card

Specialized card for displaying social media posts.

```html
<div class="card card-platform-twitter">
  <div class="card-header">
    <img src="avatar.jpg" alt="User Avatar" class="card-avatar">
    <div class="card-header-content">
      <div class="card-title">John Doe</div>
      <div class="card-subtitle">
        @johndoe
        <span class="card-timestamp">2 hours ago</span>
      </div>
    </div>
    <div class="card-platform-badge twitter">Twitter</div>
  </div>
  <div class="card-content">
    <p class="card-text">Just shipped a new feature! 🚀 Really excited about the improvements to user experience.</p>
    <div class="card-media-container aspect-16-9">
      <img src="feature-screenshot.jpg" alt="Feature Screenshot" class="card-media">
    </div>
    <div class="card-tags">
      <span class="card-tag">#development</span>
      <span class="card-tag">#ux</span>
    </div>
  </div>
  <div class="card-footer">
    <div class="metadata-row">
      <span class="metadata-item">
        <svg class="metadata-item-icon">❤️</svg>
        <span class="metadata-item-count">24</span>
      </span>
      <span class="metadata-item">
        <svg class="metadata-item-icon">💬</svg>
        <span class="metadata-item-count">8</span>
      </span>
      <span class="metadata-item">
        <svg class="metadata-item-icon">🔄</svg>
        <span class="metadata-item-count">12</span>
      </span>
    </div>
    <div class="action-buttons">
      <button class="action-button" aria-label="Share">
        <svg>...</svg>
      </button>
      <button class="action-button" aria-label="Save">
        <svg>...</svg>
      </button>
      <button class="action-button" aria-label="More options">
        <svg>...</svg>
      </button>
    </div>
  </div>
</div>
```

### Card Variations

```html
<!-- Featured Card -->
<div class="card featured-card">
  <!-- Card content -->
</div>

<!-- Quote Card -->
<div class="card quote-card">
  <div class="card-content">
    <p class="card-text">This is an inspirational quote that stands out from regular content.</p>
  </div>
</div>

<!-- Platform-specific Cards -->
<div class="card card-platform-linkedin">...</div>
<div class="card card-platform-reddit">...</div>
<div class="card card-platform-instagram">...</div>
<div class="card card-platform-pinterest">...</div>
<div class="card card-platform-web">...</div>
```

### Card Best Practices

- Always include proper alt text for images
- Use semantic HTML structure
- Ensure interactive elements have sufficient contrast
- Test keyboard navigation
- Keep content concise and scannable

---

## Button Components

### Primary Buttons

Use for the main action on a page or section.

```html
<button class="btn btn-primary">Primary Action</button>
<button class="btn btn-primary" disabled>Disabled Primary</button>
<button class="btn btn-primary loading">Loading...</button>
```

### Secondary Buttons

Use for secondary actions or when multiple buttons are present.

```html
<button class="btn btn-secondary">Secondary Action</button>
<button class="btn btn-outline">Outline Button</button>
<button class="btn btn-ghost">Ghost Button</button>
```

### Button Sizes

```html
<button class="btn btn-primary btn-sm">Small</button>
<button class="btn btn-primary">Default</button>
<button class="btn btn-primary btn-lg">Large</button>
```

### Icon Buttons

```html
<!-- Icon only -->
<button class="btn btn-icon btn-primary">
  <svg width="16" height="16">...</svg>
</button>

<!-- Icon with text -->
<button class="btn btn-primary">
  <svg width="16" height="16">...</svg>
  Save Post
</button>

<!-- Text with trailing icon -->
<button class="btn btn-secondary">
  Download
  <svg width="16" height="16">...</svg>
</button>
```

### Button Groups

```html
<div class="btn-group">
  <button class="btn btn-secondary">Left</button>
  <button class="btn btn-secondary">Center</button>
  <button class="btn btn-secondary">Right</button>
</div>
```

### Platform-specific Buttons

```html
<button class="btn btn-twitter">Share on Twitter</button>
<button class="btn btn-linkedin">Share on LinkedIn</button>
<button class="btn btn-reddit">Post to Reddit</button>
```

### Button Best Practices

- Use clear, action-oriented labels
- Maintain consistent sizing within groups
- Provide loading states for async actions
- Ensure sufficient touch targets (44px minimum)
- Use appropriate button types (button, submit, reset)

---

## Form Components

### Input Fields

```html
<div class="form-group">
  <label class="form-label" for="username">Username</label>
  <input 
    type="text" 
    id="username" 
    class="form-input" 
    placeholder="Enter your username"
    required
  >
</div>

<!-- With error state -->
<div class="form-group">
  <label class="form-label" for="email">Email</label>
  <input 
    type="email" 
    id="email" 
    class="form-input error" 
    placeholder="Enter your email"
    aria-describedby="email-error"
  >
  <div id="email-error" class="form-error">Please enter a valid email address.</div>
</div>
```

### Textarea

```html
<div class="form-group">
  <label class="form-label" for="message">Message</label>
  <textarea 
    id="message" 
    class="form-textarea" 
    placeholder="Enter your message"
    rows="4"
  ></textarea>
</div>
```

### Select Dropdown

```html
<div class="form-group">
  <label class="form-label" for="category">Category</label>
  <select id="category" class="form-select">
    <option value="">Select a category</option>
    <option value="tech">Technology</option>
    <option value="design">Design</option>
    <option value="business">Business</option>
  </select>
</div>
```

### Checkbox and Radio

```html
<!-- Checkbox -->
<div class="form-group">
  <label class="form-checkbox">
    <input type="checkbox" name="notifications">
    <span class="checkmark"></span>
    Enable notifications
  </label>
</div>

<!-- Radio buttons -->
<div class="form-group">
  <fieldset>
    <legend class="form-label">Theme Preference</legend>
    <label class="form-radio">
      <input type="radio" name="theme" value="light">
      <span class="radio-mark"></span>
      Light
    </label>
    <label class="form-radio">
      <input type="radio" name="theme" value="dark">
      <span class="radio-mark"></span>
      Dark
    </label>
    <label class="form-radio">
      <input type="radio" name="theme" value="auto">
      <span class="radio-mark"></span>
      Auto
    </label>
  </fieldset>
</div>
```

### Form Layout

```html
<form class="form">
  <div class="form-row">
    <div class="form-group">
      <label class="form-label" for="first-name">First Name</label>
      <input type="text" id="first-name" class="form-input">
    </div>
    <div class="form-group">
      <label class="form-label" for="last-name">Last Name</label>
      <input type="text" id="last-name" class="form-input">
    </div>
  </div>
  
  <div class="form-group">
    <label class="form-label" for="bio">Bio</label>
    <textarea id="bio" class="form-textarea"></textarea>
  </div>
  
  <div class="form-actions">
    <button type="button" class="btn btn-secondary">Cancel</button>
    <button type="submit" class="btn btn-primary">Save Profile</button>
  </div>
</form>
```

---

## Navigation Components

### Tab Navigation

```html
<nav class="nav" role="tablist">
  <button class="nav-item active" role="tab" aria-selected="true">
    <svg class="nav-item-icon">...</svg>
    All Posts
  </button>
  <button class="nav-item" role="tab" aria-selected="false">
    <svg class="nav-item-icon">...</svg>
    Twitter
  </button>
  <button class="nav-item" role="tab" aria-selected="false">
    <svg class="nav-item-icon">...</svg>
    LinkedIn
  </button>
</nav>
```

### Breadcrumb Navigation

```html
<nav class="breadcrumb" aria-label="Breadcrumb">
  <ol class="breadcrumb-list">
    <li class="breadcrumb-item">
      <a href="/" class="breadcrumb-link">Home</a>
    </li>
    <li class="breadcrumb-item">
      <a href="/posts" class="breadcrumb-link">Posts</a>
    </li>
    <li class="breadcrumb-item active" aria-current="page">
      Post Details
    </li>
  </ol>
</nav>
```

### Sidebar Navigation

```html
<nav class="sidebar-nav">
  <div class="sidebar-section">
    <h3 class="sidebar-title">Main</h3>
    <a href="/" class="sidebar-item active">
      <svg class="sidebar-icon">...</svg>
      Dashboard
    </a>
    <a href="/posts" class="sidebar-item">
      <svg class="sidebar-icon">...</svg>
      Posts
    </a>
  </div>
  
  <div class="sidebar-section">
    <h3 class="sidebar-title">Platforms</h3>
    <a href="/twitter" class="sidebar-item">
      <svg class="sidebar-icon">...</svg>
      Twitter
    </a>
    <a href="/linkedin" class="sidebar-item">
      <svg class="sidebar-icon">...</svg>
      LinkedIn
    </a>
  </div>
</nav>
```

---

## Modal Components

### Basic Modal

```html
<div class="modal-backdrop show">
  <div class="modal show" role="dialog" aria-labelledby="modal-title" aria-modal="true">
    <div class="modal-header">
      <h2 id="modal-title" class="modal-title">Confirm Action</h2>
      <button class="modal-close" aria-label="Close modal">
        <svg>...</svg>
      </button>
    </div>
    <div class="modal-body">
      <p>Are you sure you want to delete this post? This action cannot be undone.</p>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary">Cancel</button>
      <button class="btn btn-error">Delete</button>
    </div>
  </div>
</div>
```

### Form Modal

```html
<div class="modal-backdrop show">
  <div class="modal show modal-lg">
    <div class="modal-header">
      <h2 class="modal-title">Edit Profile</h2>
      <button class="modal-close" aria-label="Close modal">×</button>
    </div>
    <div class="modal-body">
      <form>
        <div class="form-group">
          <label class="form-label" for="modal-name">Name</label>
          <input type="text" id="modal-name" class="form-input">
        </div>
        <div class="form-group">
          <label class="form-label" for="modal-bio">Bio</label>
          <textarea id="modal-bio" class="form-textarea"></textarea>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary">Cancel</button>
      <button class="btn btn-primary">Save Changes</button>
    </div>
  </div>
</div>
```

---

## Badge Components

### Status Badges

```html
<span class="badge badge-success">Active</span>
<span class="badge badge-warning">Pending</span>
<span class="badge badge-error">Error</span>
<span class="badge badge-secondary">Draft</span>
```

### Count Badges

```html
<button class="btn btn-secondary btn-with-badge">
  Notifications
  <span class="btn-badge">3</span>
</button>
```

### Platform Badges

```html
<span class="platform-badge platform-badge-twitter">Twitter</span>
<span class="platform-badge platform-badge-linkedin">LinkedIn</span>
<span class="platform-badge platform-badge-reddit">Reddit</span>
```

---

## Alert Components

### Alert Types

```html
<div class="alert alert-success">
  <svg class="alert-icon">✓</svg>
  <div>
    <div class="alert-title">Success!</div>
    Your post has been saved successfully.
  </div>
  <button class="alert-close">×</button>
</div>

<div class="alert alert-warning">
  <svg class="alert-icon">⚠</svg>
  <div>
    <div class="alert-title">Warning</div>
    Your storage is almost full. Consider upgrading your plan.
  </div>
</div>

<div class="alert alert-error">
  <svg class="alert-icon">✕</svg>
  <div>
    <div class="alert-title">Error</div>
    Failed to save post. Please try again.
  </div>
</div>
```

---

## Loading Components

### Skeleton Loading

```html
<div class="card">
  <div class="card-header">
    <div class="skeleton skeleton-circle"></div>
    <div>
      <div class="skeleton skeleton-text" style="width: 120px;"></div>
      <div class="skeleton skeleton-text" style="width: 80px;"></div>
    </div>
  </div>
  <div class="card-content">
    <div class="skeleton skeleton-text"></div>
    <div class="skeleton skeleton-text"></div>
    <div class="skeleton skeleton-text" style="width: 60%;"></div>
    <div class="skeleton skeleton-card"></div>
  </div>
</div>
```

### Loading Spinner

```html
<div class="loading-spinner">
  <svg class="spinner" viewBox="0 0 50 50">
    <circle class="path" cx="25" cy="25" r="20" fill="none" stroke="currentColor" stroke-width="2" stroke-miterlimit="10"/>
  </svg>
</div>
```

---

## Platform Components

### Platform Icons

```html
<div class="platform-icon platform-twitter">
  <svg>...</svg>
</div>

<div class="platform-logo twitter"></div>
<div class="platform-logo linkedin"></div>
<div class="platform-logo reddit"></div>
```

### Platform Indicators

```html
<div class="card platform-indicator twitter">
  <!-- Card content -->
</div>
```

---

## Layout Components

### Grid Layouts

```html
<!-- Responsive card grid -->
<div class="grid grid-auto-fit gap-6">
  <div class="card">Card 1</div>
  <div class="card">Card 2</div>
  <div class="card">Card 3</div>
</div>

<!-- Fixed column grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <div>Column 1</div>
  <div>Column 2</div>
  <div>Column 3</div>
</div>
```

### Flex Layouts

```html
<!-- Header layout -->
<header class="flex items-center justify-between p-4">
  <div class="flex items-center gap-3">
    <img src="logo.svg" alt="Logo" class="h-8">
    <h1 class="text-xl font-semibold">Notely</h1>
  </div>
  <nav class="flex items-center gap-4">
    <a href="/settings">Settings</a>
    <button class="btn btn-primary">Sign Out</button>
  </nav>
</header>

<!-- Content with sidebar -->
<div class="flex">
  <aside class="w-64 bg-surface border-r">
    <!-- Sidebar content -->
  </aside>
  <main class="flex-1 p-6">
    <!-- Main content -->
  </main>
</div>
```

### Container Layouts

```html
<!-- Centered content -->
<div class="container mx-auto">
  <h1>Page Title</h1>
  <p>Page content goes here.</p>
</div>

<!-- Different container sizes -->
<div class="container container-sm">Small container</div>
<div class="container container-lg">Large container</div>
```

---

## Accessibility Guidelines

### Keyboard Navigation

- Ensure all interactive elements are keyboard accessible
- Provide visible focus indicators
- Implement logical tab order
- Support standard keyboard shortcuts

### Screen Reader Support

- Use semantic HTML elements
- Provide proper ARIA labels and descriptions
- Include alternative text for images
- Use headings to create document structure

### Color and Contrast

- Maintain sufficient color contrast ratios
- Don't rely solely on color to convey information
- Test with color blindness simulators
- Provide high contrast mode support

### Motion and Animation

- Respect `prefers-reduced-motion` settings
- Provide alternatives to motion-based interactions
- Keep animations subtle and purposeful
- Allow users to disable animations

---

## Performance Considerations

### CSS Optimization

- Use efficient selectors
- Minimize specificity conflicts
- Leverage CSS custom properties
- Optimize for critical rendering path

### Image Optimization

- Use appropriate image formats
- Implement lazy loading
- Provide responsive images
- Include proper alt text

### JavaScript Integration

- Use progressive enhancement
- Minimize DOM manipulation
- Implement efficient event handling
- Consider component lifecycle

---

## Testing Guidelines

### Visual Testing

- Test across different browsers
- Verify responsive behavior
- Check color contrast ratios
- Validate with design specifications

### Functional Testing

- Test keyboard navigation
- Verify screen reader compatibility
- Check form validation
- Test error states

### Performance Testing

- Measure loading times
- Check for layout shifts
- Monitor memory usage
- Test on slower devices

---

This component guide should be used alongside the main design system documentation to ensure consistent and accessible implementation across the Notely application.