/* 
 * Typography System for Notely
 * A comprehensive typography system with improved readability and accessibility
 */

/* ===== FONT IMPORTS ===== */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* ===== BASE TYPOGRAPHY ===== */

html {
  font-size: 100%; /* Default font size */
  -webkit-text-size-adjust: 100%; /* Prevent font scaling in landscape */
  -ms-text-size-adjust: 100%; /* Prevent font scaling in IE */
}

body {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== HEADINGS ===== */

h1, .h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin-bottom: 1.5rem;
  color: var(--color-text-primary);
}

h2, .h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin-bottom: 1.25rem;
  color: var(--color-text-primary);
}

h3, .h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin-bottom: 1rem;
  color: var(--color-text-primary);
}

h4, .h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-normal);
  margin-bottom: 0.75rem;
  color: var(--color-text-primary);
}

h5, .h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-normal);
  margin-bottom: 0.5rem;
  color: var(--color-text-primary);
}

h6, .h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-normal);
  margin-bottom: 0.5rem;
  color: var(--color-text-primary);
}

/* ===== BODY TEXT ===== */

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

.lead {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
}

.small {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.tiny {
  font-size: var(--font-size-xs);
  line-height: var(--line-height-normal);
}

/* ===== TEXT UTILITIES ===== */

/* Font sizes */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.text-4xl {
  font-size: var(--font-size-4xl);
}

/* Font weights */
.font-light {
  font-weight: var(--font-weight-light);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

/* Line heights */
.leading-none {
  line-height: var(--line-height-none);
}

.leading-tight {
  line-height: var(--line-height-tight);
}

.leading-snug {
  line-height: var(--line-height-snug);
}

.leading-normal {
  line-height: var(--line-height-normal);
}

.leading-relaxed {
  line-height: var(--line-height-relaxed);
}

.leading-loose {
  line-height: var(--line-height-loose);
}

/* Letter spacing */
.tracking-tighter {
  letter-spacing: var(--letter-spacing-tighter);
}

.tracking-tight {
  letter-spacing: var(--letter-spacing-tight);
}

.tracking-normal {
  letter-spacing: var(--letter-spacing-normal);
}

.tracking-wide {
  letter-spacing: var(--letter-spacing-wide);
}

.tracking-wider {
  letter-spacing: var(--letter-spacing-wider);
}

.tracking-widest {
  letter-spacing: var(--letter-spacing-widest);
}

/* Text colors */
.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-tertiary {
  color: var(--color-text-tertiary);
}

.text-brand {
  color: var(--color-primary);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-error {
  color: var(--color-error);
}

.text-info {
  color: var(--color-info);
}

/* Text alignment */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

/* Text transforms */
.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.normal-case {
  text-transform: none;
}

/* Text decoration */
.underline {
  text-decoration: underline;
}

.line-through {
  text-decoration: line-through;
}

.no-underline {
  text-decoration: none;
}

/* Text overflow */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overflow-ellipsis {
  text-overflow: ellipsis;
}

.overflow-clip {
  text-overflow: clip;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

/* Multi-line truncation */
.line-clamp-1,
.line-clamp-2,
.line-clamp-3,
.line-clamp-4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-1 {
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  -webkit-line-clamp: 4;
}

/* ===== LINKS ===== */

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-200) var(--ease-out);
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

a:focus-visible {
  outline: 2px solid var(--focus-ring-color);
  outline-offset: 2px;
  text-decoration: none;
}

/* External link indicator */
a[target="_blank"]::after {
  content: " ↗";
  display: inline-block;
}

/* ===== LISTS ===== */

ul, ol {
  margin-top: 0;
  margin-bottom: 1rem;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.5rem;
}

/* Unstyled list */
.list-none {
  list-style-type: none;
  padding-left: 0;
}

/* Styled lists */
.list-disc {
  list-style-type: disc;
}

.list-decimal {
  list-style-type: decimal;
}

/* ===== BLOCKQUOTE ===== */

blockquote {
  margin: 0 0 1rem;
  padding: 1rem;
  border-left: 0.25rem solid var(--color-border);
  color: var(--color-text-secondary);
  font-style: italic;
}

blockquote p:last-child {
  margin-bottom: 0;
}

blockquote cite {
  display: block;
  margin-top: 0.5rem;
  font-size: var(--font-size-sm);
  font-style: normal;
}

/* ===== CODE ===== */

code {
  font-family: var(--font-mono);
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--color-text-primary);
}

pre {
  font-family: var(--font-mono);
  margin-top: 0;
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: rgba(0, 0, 0, 0.05);
  overflow-x: auto;
}

pre code {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */

@media (max-width: 1023px) {
  h1, .h1 {
    font-size: var(--font-size-3xl);
  }
  
  h2, .h2 {
    font-size: var(--font-size-2xl);
  }
  
  h3, .h3 {
    font-size: var(--font-size-xl);
  }
  
  h4, .h4 {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 767px) {
  h1, .h1 {
    font-size: var(--font-size-2xl);
  }
  
  h2, .h2 {
    font-size: var(--font-size-xl);
  }
  
  h3, .h3 {
    font-size: var(--font-size-lg);
  }
  
  h4, .h4 {
    font-size: var(--font-size-base);
  }
  
  .lead {
    font-size: var(--font-size-base);
  }
}

/* ===== ACCESSIBILITY ===== */

/* High contrast mode */
@media (forced-colors: active) {
  a {
    color: LinkText;
  }
  
  a:hover {
    color: Highlight;
  }
  
  code, pre {
    border: 1px solid CanvasText;
  }
  
  blockquote {
    border-left-color: CanvasText;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  a {
    transition: none;
  }
}