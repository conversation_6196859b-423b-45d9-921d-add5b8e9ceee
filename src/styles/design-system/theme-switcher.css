/* 
 * Theme Switcher
 * Handles theme switching functionality based on user preference and system settings
 */

/* Default to light theme */
:root {
  color-scheme: light;
}

/* Apply light theme explicitly */
.light-theme {
  color-scheme: light;
}

/* Apply dark theme explicitly */
.dark-theme {
  color-scheme: dark;
}

/* Auto theme detection based on system preference */
@media (prefers-color-scheme: dark) {
  :root:not(.light-theme):not(.dark-theme) {
    /* Import dark theme variables */
    color-scheme: dark;
    
    /* Semantic Colors */
    --color-background: var(--color-gray-900);
    --color-surface: var(--color-gray-800);
    --color-surface-hover: var(--color-gray-700);
    --color-border: var(--color-gray-700);
    --color-border-hover: var(--color-gray-600);
    --color-text-primary: var(--color-gray-50);
    --color-text-secondary: var(--color-gray-300);
    --color-text-tertiary: var(--color-gray-400);
    
    /* Component-specific */
    --card-background: var(--color-surface);
    --card-border-color: var(--color-border);
    --card-shadow: var(--shadow-sm-dark);
    --card-shadow-hover: var(--shadow-md-dark);
    
    /* Dark theme specific shadows with adjusted opacity */
    --shadow-sm-dark: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
    --shadow-md-dark: 0 4px 6px rgba(0, 0, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-lg-dark: 0 10px 15px rgba(0, 0, 0, 0.5), 0 4px 6px rgba(0, 0, 0, 0.4);
    --shadow-xl-dark: 0 20px 25px rgba(0, 0, 0, 0.5), 0 10px 10px rgba(0, 0, 0, 0.4);
  }
}

/* Transition for smooth theme switching */
body {
  transition: background-color var(--duration-300) var(--ease-in-out),
              color var(--duration-300) var(--ease-in-out);
}

/* Disable transitions when prefers-reduced-motion is enabled */
@media (prefers-reduced-motion: reduce) {
  body {
    transition: none;
  }
}