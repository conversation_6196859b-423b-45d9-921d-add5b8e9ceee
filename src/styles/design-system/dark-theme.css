/* 
 * Dark Theme
 * Defines color tokens specific to the dark theme
 */

.dark-theme {
  /* Semantic Colors */
  --color-background: var(--color-gray-900);
  --color-surface: var(--color-gray-800);
  --color-surface-hover: var(--color-gray-700);
  --color-border: var(--color-gray-700);
  --color-border-hover: var(--color-gray-600);
  --color-text-primary: var(--color-gray-50);
  --color-text-secondary: var(--color-gray-300);
  --color-text-tertiary: var(--color-gray-400);
  
  /* Component-specific */
  --card-background: var(--color-surface);
  --card-border-color: var(--color-border);
  --card-shadow: var(--shadow-sm-dark);
  --card-shadow-hover: var(--shadow-md-dark);
  
  /* Dark theme specific shadows with adjusted opacity */
  --shadow-sm-dark: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
  --shadow-md-dark: 0 4px 6px rgba(0, 0, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-lg-dark: 0 10px 15px rgba(0, 0, 0, 0.5), 0 4px 6px rgba(0, 0, 0, 0.4);
  --shadow-xl-dark: 0 20px 25px rgba(0, 0, 0, 0.5), 0 10px 10px rgba(0, 0, 0, 0.4);
  
  /* Platform-specific colors */
  --platform-twitter-bg: rgba(29, 161, 242, 0.15);
  --platform-twitter-border: rgba(29, 161, 242, 0.4);
  --platform-linkedin-bg: rgba(0, 119, 181, 0.15);
  --platform-linkedin-border: rgba(0, 119, 181, 0.4);
  --platform-reddit-bg: rgba(255, 69, 0, 0.15);
  --platform-reddit-border: rgba(255, 69, 0, 0.4);
  --platform-instagram-bg: rgba(225, 48, 108, 0.15);
  --platform-instagram-border: rgba(225, 48, 108, 0.4);
  --platform-pinterest-bg: rgba(230, 0, 35, 0.15);
  --platform-pinterest-border: rgba(230, 0, 35, 0.4);
  --platform-web-bg: rgba(16, 185, 129, 0.15);
  --platform-web-border: rgba(16, 185, 129, 0.4);
}