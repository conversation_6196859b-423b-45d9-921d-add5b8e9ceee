/* 
 * Text Truncation Utilities
 * A set of utilities for handling text overflow consistently across the application.
 */

/* Single Line Truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Multi-line Truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  overflow: hidden;
}

.line-clamp-5 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
  line-clamp: 5;
  overflow: hidden;
}

.line-clamp-6 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
  line-clamp: 6;
  overflow: hidden;
}

/* Gradient Fade for Text Truncation */
.truncate-fade {
  position: relative;
  overflow: hidden;
  max-height: 4.5em; /* Approximately 3 lines of text */
}

.truncate-fade::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 1.5em;
  background: linear-gradient(
    to bottom,
    transparent,
    var(--color-surface)
  );
  pointer-events: none;
}

/* Dark mode support for gradient fade */
.dark-theme .truncate-fade::after {
  background: linear-gradient(
    to bottom,
    transparent,
    var(--color-surface)
  );
}

/* Truncate with "Read More" Button */
.truncate-with-action {
  position: relative;
}

.truncate-with-action .truncate-content {
  overflow: hidden;
  max-height: 4.5em; /* Approximately 3 lines of text */
  transition: max-height 0.3s ease;
}

.truncate-with-action .truncate-content.expanded {
  max-height: none;
}

.truncate-with-action .truncate-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2em;
  background: linear-gradient(
    to bottom,
    transparent,
    var(--color-surface)
  );
  pointer-events: none;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.truncate-with-action .truncate-content.expanded + .truncate-fade {
  opacity: 0;
}

.truncate-with-action .truncate-button {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--color-surface);
  padding: 0 0.5em;
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  cursor: pointer;
  z-index: 1;
}

/* Responsive Truncation */
@media (max-width: 768px) {
  .md\:truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .md\:line-clamp-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
  }
  
  .md\:line-clamp-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    overflow: hidden;
  }
}

@media (max-width: 480px) {
  .sm\:truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .sm\:line-clamp-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
  }
}

/* Accessibility Considerations */
@media (prefers-reduced-motion: reduce) {
  .truncate-with-action .truncate-content {
    transition: none;
  }
  
  .truncate-with-action .truncate-fade {
    transition: none;
  }
}

/* JavaScript Helper for Truncate with Action */
/*
  // Add this JavaScript to make the truncate-with-action work:
  
  document.querySelectorAll('.truncate-with-action .truncate-button').forEach(button => {
    button.addEventListener('click', () => {
      const content = button.parentElement.querySelector('.truncate-content');
      content.classList.toggle('expanded');
      button.textContent = content.classList.contains('expanded') ? 'Read Less' : 'Read More';
    });
  });
*/