/* 
 * Design System Index
 * Imports all design system files in the correct order
 */

/* Import Inter font for consistent typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Base design tokens */
@import './tokens.css';

/* Theme-specific tokens */
@import './light-theme.css';
@import './dark-theme.css';

/* Theme switching functionality */
@import './theme-switcher.css';

/* 
 * Usage:
 * 
 * 1. Import this file in your main CSS or entry point:
 *    @import './styles/design-system/index.css';
 * 
 * 2. Apply theme classes to the root element:
 *    - For light theme: <html class="light-theme">
 *    - For dark theme: <html class="dark-theme">
 *    - For auto theme (based on system preference): <html>
 * 
 * 3. Use design tokens in your CSS:
 *    .my-element {
 *      color: var(--color-text-primary);
 *      background-color: var(--color-surface);
 *      padding: var(--space-4);
 *      border-radius: var(--radius-md);
 *    }
 */