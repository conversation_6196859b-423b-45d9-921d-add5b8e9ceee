/* 
 * Design Tokens for Notely
 * Core design variables with improved color contrast for accessibility
 */

:root {
  /* ===== COLOR SYSTEM ===== */
  
  /* Base colors with improved contrast */
  --color-primary: #4f46e5; /* WCAG AAA compliant */
  --color-primary-light: #6366f1;
  --color-primary-dark: #4338ca;
  
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* Semantic colors with improved contrast */
  --color-success: #10b981; /* WCAG AA compliant */
  --color-success-dark: #059669; /* WCAG AAA compliant */
  --color-warning: #d97706; /* WCAG AA compliant */
  --color-warning-dark: #b45309; /* WCAG AAA compliant */
  --color-error: #dc2626; /* WCAG AA compliant */
  --color-error-dark: #b91c1c; /* WCAG AAA compliant */
  --color-info: #2563eb; /* WCAG AA compliant */
  --color-info-dark: #1d4ed8; /* WCAG AAA compliant */
  
  /* Platform colors with improved contrast */
  --color-twitter: #1a8cd8; /* WCAG AA compliant */
  --color-linkedin: #0a66c2; /* WCAG AA compliant */
  --color-reddit: #ff4500; /* WCAG AA compliant */
  --color-instagram: #c13584; /* WCAG AA compliant */
  --color-pinterest: #e60023; /* WCAG AA compliant */
  --color-web: #10b981; /* WCAG AA compliant */
  
  /* Dark theme colors with improved contrast */
  --color-background-dark: #121212;
  --color-surface-dark: #1e1e1e;
  --color-surface-hover-dark: #2a2a2a;
  --color-text-primary-dark: rgba(255, 255, 255, 0.95); /* WCAG AAA compliant */
  --color-text-secondary-dark: rgba(255, 255, 255, 0.8); /* WCAG AA compliant */
  --color-text-tertiary-dark: rgba(255, 255, 255, 0.6); /* WCAG AA compliant */
  --color-border-dark: rgba(255, 255, 255, 0.15);
  --color-border-hover-dark: rgba(255, 255, 255, 0.25);
  
  /* Light theme colors with improved contrast */
  --color-background-light: #ffffff;
  --color-surface-light: #ffffff;
  --color-surface-hover-light: #f3f4f6;
  --color-text-primary-light: #111827; /* WCAG AAA compliant */
  --color-text-secondary-light: #374151; /* WCAG AAA compliant */
  --color-text-tertiary-light: #6b7280; /* WCAG AA compliant */
  --color-border-light: #e5e7eb;
  --color-border-hover-light: #d1d5db;
  
  /* Theme colors (default to dark theme) */
  --color-background: var(--color-background-dark);
  --color-surface: var(--color-surface-dark);
  --color-surface-hover: var(--color-surface-hover-dark);
  --color-text-primary: var(--color-text-primary-dark);
  --color-text-secondary: var(--color-text-secondary-dark);
  --color-text-tertiary: var(--color-text-tertiary-dark);
  --color-border: var(--color-border-dark);
  --color-border-hover: var(--color-border-hover-dark);
  
  /* Focus ring */
  --focus-ring-color: rgba(79, 70, 229, 0.5);
  
  /* ===== TYPOGRAPHY SYSTEM ===== */
  
  /* Font family */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --font-serif: ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
  
  /* Font sizes */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  --font-size-5xl: 3rem;     /* 48px */
  --font-size-6xl: 3.75rem;  /* 60px */
  --font-size-7xl: 4.5rem;   /* 72px */
  --font-size-8xl: 6rem;     /* 96px */
  --font-size-9xl: 8rem;     /* 128px */
  
  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Letter spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
  
  /* ===== SPACING SYSTEM ===== */
  
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.5rem;   /* 24px */
  --space-6: 2rem;     /* 32px */
  --space-8: 3rem;     /* 48px */
  --space-10: 4rem;    /* 64px */
  --space-12: 6rem;    /* 96px */
  --space-16: 8rem;    /* 128px */
  --space-20: 10rem;   /* 160px */
  --space-24: 12rem;   /* 192px */
  --space-32: 16rem;   /* 256px */
  --space-40: 20rem;   /* 320px */
  --space-48: 24rem;   /* 384px */
  --space-56: 28rem;   /* 448px */
  --space-64: 32rem;   /* 512px */
  
  /* ===== BORDER RADIUS ===== */
  
  --radius-sm: 0.125rem;  /* 2px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-3xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;
  
  /* ===== SHADOWS ===== */
  
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* ===== ANIMATION ===== */
  
  /* Animation durations */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;
  
  /* Easing functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
  
  /* ===== BORDER WIDTH ===== */
  
  --border-width-1: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-width-8: 8px;
  
  /* ===== Z-INDEX ===== */
  
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;
}

/* Light theme class */
.light-theme {
  --color-background: var(--color-background-light);
  --color-surface: var(--color-surface-light);
  --color-surface-hover: var(--color-surface-hover-light);
  --color-text-primary: var(--color-text-primary-light);
  --color-text-secondary: var(--color-text-secondary-light);
  --color-text-tertiary: var(--color-text-tertiary-light);
  --color-border: var(--color-border-light);
  --color-border-hover: var(--color-border-hover-light);
}

/* High contrast mode adjustments */
@media (forced-colors: active) {
  :root {
    --color-primary: Highlight;
    --color-primary-light: Highlight;
    --color-primary-dark: Highlight;
    --color-success: Highlight;
    --color-success-dark: Highlight;
    --color-warning: Highlight;
    --color-warning-dark: Highlight;
    --color-error: Highlight;
    --color-error-dark: Highlight;
    --color-info: Highlight;
    --color-info-dark: Highlight;
    
    --color-background: Canvas;
    --color-surface: Canvas;
    --color-surface-hover: Canvas;
    --color-text-primary: CanvasText;
    --color-text-secondary: CanvasText;
    --color-text-tertiary: CanvasText;
    --color-border: CanvasText;
    --color-border-hover: CanvasText;
    
    --focus-ring-color: Highlight;
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  :root {
    --duration-75: 0ms;
    --duration-100: 0ms;
    --duration-150: 0ms;
    --duration-200: 0ms;
    --duration-300: 0ms;
    --duration-500: 0ms;
    --duration-700: 0ms;
    --duration-1000: 0ms;
  }
}