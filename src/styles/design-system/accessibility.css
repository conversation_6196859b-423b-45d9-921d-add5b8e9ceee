/* 
 * Accessibility Styles for Note<PERSON>
 * Enhances the interface for better accessibility across different user needs
 */

/* ===== FOCUS MANAGEMENT ===== */

/* Focus outline styles */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 0.25rem;
}

/* High contrast focus styles */
@media (forced-colors: active) {
  :focus-visible {
    outline: 2px solid Highlight;
    outline-offset: 2px;
  }
}

/* Focus styles for interactive elements */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[role="button"]:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.2);
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background-color: var(--color-primary);
  color: white;
  padding: 0.5rem 1rem;
  z-index: 100;
  transition: top 0.2s ease;
}

.skip-link:focus {
  top: 0;
}

/* ===== SCREEN READER UTILITIES ===== */

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Screen reader only, but focusable */
.sr-only-focusable:not(:focus) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* ===== REDUCED MOTION ===== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== COLOR CONTRAST ===== */

/* High contrast mode adjustments */
@media (forced-colors: active) {
  /* Ensure buttons have proper contrast */
  button,
  .btn {
    border: 1px solid ButtonText;
    color: ButtonText;
    background-color: ButtonFace;
  }
  
  button:hover,
  .btn:hover {
    border-color: Highlight;
    color: Highlight;
  }
  
  /* Ensure links have proper contrast */
  a {
    color: LinkText;
  }
  
  a:hover {
    color: Highlight;
  }
  
  /* Ensure form controls have proper contrast */
  input,
  select,
  textarea {
    border: 1px solid ButtonText;
    background-color: Field;
    color: FieldText;
  }
  
  /* Ensure checkboxes and radio buttons have proper contrast */
  input[type="checkbox"],
  input[type="radio"] {
    border: 1px solid ButtonText;
    background-color: ButtonFace;
  }
  
  input[type="checkbox"]:checked,
  input[type="radio"]:checked {
    background-color: Highlight;
    border-color: Highlight;
  }
  
  /* Ensure cards have proper contrast */
  .card,
  .post-card,
  .dramatic-card {
    border: 1px solid ButtonText;
    background-color: Canvas;
    color: CanvasText;
  }
  
  /* Ensure icons have proper contrast */
  svg {
    fill: currentColor;
    stroke: currentColor;
  }
}

/* Non-color indicators for important information */
.required-field::after {
  content: '*';
  color: var(--color-error);
  margin-left: 0.25rem;
}

.error-indicator {
  display: flex;
  align-items: center;
}

.error-indicator::before {
  content: '!';
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: var(--color-error);
  color: white;
  font-weight: bold;
  margin-right: 0.5rem;
}

.success-indicator {
  display: flex;
  align-items: center;
}

.success-indicator::before {
  content: '✓';
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: var(--color-success);
  color: white;
  font-weight: bold;
  margin-right: 0.5rem;
}

/* ===== KEYBOARD NAVIGATION ===== */

/* Ensure all interactive elements are keyboard accessible */
[tabindex="-1"] {
  outline: none;
}

/* Ensure proper tab order */
.tab-order-first {
  order: -1;
}

.tab-order-last {
  order: 999;
}

/* ===== ARIA ATTRIBUTES ===== */

/* Ensure proper ARIA attributes for common components */

/* Toggle button */
.toggle-button[aria-pressed="true"] {
  background-color: var(--color-primary);
  color: white;
}

/* Accordion */
.accordion-button[aria-expanded="true"] .accordion-icon {
  transform: rotate(180deg);
}

.accordion-panel[aria-hidden="true"] {
  display: none;
}

/* Tabs */
.tab[aria-selected="true"] {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

.tab-panel[aria-hidden="true"] {
  display: none;
}

/* Modal */
.modal[aria-hidden="true"] {
  display: none;
}

/* ===== TEXT SIZE ADJUSTMENTS ===== */

/* Ensure text remains readable when text size is increased */
html {
  font-size: 100%; /* Default font size */
}

@media (max-width: 768px) {
  html {
    font-size: 100%; /* Maintain font size on mobile */
  }
}

/* Ensure containers adapt to text size */
.container {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Ensure text doesn't overflow */
.text-wrap {
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
}

/* ===== FORM ACCESSIBILITY ===== */

/* Associate labels with form controls */
label {
  display: block;
  margin-bottom: 0.5rem;
}

/* Ensure form controls have proper padding for touch targets */
input,
select,
textarea,
button {
  padding: 0.5rem;
  font-size: 1rem;
}

/* Ensure error messages are associated with form controls */
.form-error-message {
  color: var(--color-error);
  margin-top: 0.25rem;
  font-size: 0.875rem;
}

/* ===== IMAGE ACCESSIBILITY ===== */

/* Ensure images have proper alt text */
img:not([alt]) {
  outline: 2px dashed var(--color-error);
  outline-offset: 2px;
}

/* Ensure SVGs are accessible */
svg {
  fill: currentColor;
}

svg[role="img"]:not([aria-label]) {
  outline: 2px dashed var(--color-error);
  outline-offset: 2px;
}

/* ===== TABLE ACCESSIBILITY ===== */

/* Ensure tables are accessible */
table {
  border-collapse: collapse;
  width: 100%;
}

caption {
  text-align: left;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

th {
  text-align: left;
  font-weight: bold;
}

th,
td {
  padding: 0.5rem;
  border: 1px solid var(--color-border);
}

/* ===== LINK ACCESSIBILITY ===== */

/* Ensure links are distinguishable */
a {
  color: var(--color-primary);
  text-decoration: underline;
}

a:hover {
  color: var(--color-primary-dark);
}

a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Ensure external links are identifiable */
a[target="_blank"]::after {
  content: " ↗";
  display: inline-block;
}

/* ===== BUTTON ACCESSIBILITY ===== */

/* Ensure buttons have proper padding for touch targets */
button,
.btn {
  min-height: 44px;
  min-width: 44px;
  padding: 0.5rem 1rem;
}

/* Ensure disabled buttons are properly styled */
button:disabled,
.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ===== DIALOG ACCESSIBILITY ===== */

/* Ensure dialogs are properly styled */
dialog {
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  max-width: 100%;
  max-height: 100vh;
  overflow-y: auto;
}

dialog::backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

/* ===== NOTIFICATION ACCESSIBILITY ===== */

/* Ensure notifications are properly styled */
.notification {
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  margin-bottom: 1rem;
}

.notification-success {
  border-color: var(--color-success);
  background-color: rgba(16, 185, 129, 0.1);
}

.notification-error {
  border-color: var(--color-error);
  background-color: rgba(239, 68, 68, 0.1);
}

.notification-warning {
  border-color: var(--color-warning);
  background-color: rgba(245, 158, 11, 0.1);
}

.notification-info {
  border-color: var(--color-info);
  background-color: rgba(59, 130, 246, 0.1);
}