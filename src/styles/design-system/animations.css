/* 
 * Animation System for Notely
 * A comprehensive set of animation utilities and transitions
 */

/* ===== ANIMATION VARIABLES ===== */

:root {
  /* Animation durations */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;
  
  /* Easing functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
  
  /* Reduced motion preference */
  --reduced-motion-duration: 0.01ms;
}

/* ===== TRANSITION UTILITIES ===== */

/* Transition property utilities */
.transition-all {
  transition-property: all;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
}

.transition-opacity {
  transition-property: opacity;
}

.transition-shadow {
  transition-property: box-shadow;
}

.transition-transform {
  transition-property: transform;
}

/* Transition duration utilities */
.duration-75 {
  transition-duration: var(--duration-75);
}

.duration-100 {
  transition-duration: var(--duration-100);
}

.duration-150 {
  transition-duration: var(--duration-150);
}

.duration-200 {
  transition-duration: var(--duration-200);
}

.duration-300 {
  transition-duration: var(--duration-300);
}

.duration-500 {
  transition-duration: var(--duration-500);
}

.duration-700 {
  transition-duration: var(--duration-700);
}

.duration-1000 {
  transition-duration: var(--duration-1000);
}

/* Transition timing function utilities */
.ease-linear {
  transition-timing-function: var(--ease-linear);
}

.ease-in {
  transition-timing-function: var(--ease-in);
}

.ease-out {
  transition-timing-function: var(--ease-out);
}

.ease-in-out {
  transition-timing-function: var(--ease-in-out);
}

.ease-bounce {
  transition-timing-function: var(--ease-bounce);
}

/* Transition delay utilities */
.delay-75 {
  transition-delay: var(--duration-75);
}

.delay-100 {
  transition-delay: var(--duration-100);
}

.delay-150 {
  transition-delay: var(--duration-150);
}

.delay-200 {
  transition-delay: var(--duration-200);
}

.delay-300 {
  transition-delay: var(--duration-300);
}

.delay-500 {
  transition-delay: var(--duration-500);
}

.delay-700 {
  transition-delay: var(--duration-700);
}

.delay-1000 {
  transition-delay: var(--duration-1000);
}

/* ===== ANIMATION KEYFRAMES ===== */

/* Fade in */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Fade out */
@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Slide in from top */
@keyframes slide-in-top {
  from {
    transform: translateY(-1rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Slide in from bottom */
@keyframes slide-in-bottom {
  from {
    transform: translateY(1rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Slide in from left */
@keyframes slide-in-left {
  from {
    transform: translateX(-1rem);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Slide in from right */
@keyframes slide-in-right {
  from {
    transform: translateX(1rem);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Scale up */
@keyframes scale-up {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Scale down */
@keyframes scale-down {
  from {
    transform: scale(1.05);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Bounce */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Pulse */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Spin */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Ping */
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Ripple */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Shimmer */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ===== ANIMATION UTILITIES ===== */

/* Animation utilities */
.animate-fade-in {
  animation: fade-in var(--duration-300) var(--ease-out) forwards;
}

.animate-fade-out {
  animation: fade-out var(--duration-300) var(--ease-in) forwards;
}

.animate-slide-in-top {
  animation: slide-in-top var(--duration-300) var(--ease-out) forwards;
}

.animate-slide-in-bottom {
  animation: slide-in-bottom var(--duration-300) var(--ease-out) forwards;
}

.animate-slide-in-left {
  animation: slide-in-left var(--duration-300) var(--ease-out) forwards;
}

.animate-slide-in-right {
  animation: slide-in-right var(--duration-300) var(--ease-out) forwards;
}

.animate-scale-up {
  animation: scale-up var(--duration-300) var(--ease-out) forwards;
}

.animate-scale-down {
  animation: scale-down var(--duration-300) var(--ease-out) forwards;
}

.animate-bounce {
  animation: bounce var(--duration-1000) infinite;
}

.animate-pulse {
  animation: pulse var(--duration-1000) cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin var(--duration-1000) linear infinite;
}

.animate-ping {
  animation: ping var(--duration-1000) cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-ripple {
  animation: ripple var(--duration-700) linear forwards;
}

.animate-shimmer {
  background: linear-gradient(90deg, 
              rgba(255, 255, 255, 0.03) 25%, 
              rgba(255, 255, 255, 0.08) 50%, 
              rgba(255, 255, 255, 0.03) 75%);
  background-size: 200% 100%;
  animation: shimmer var(--duration-1000) infinite;
}

/* Animation delay utilities */
.animation-delay-75 {
  animation-delay: var(--duration-75);
}

.animation-delay-100 {
  animation-delay: var(--duration-100);
}

.animation-delay-150 {
  animation-delay: var(--duration-150);
}

.animation-delay-200 {
  animation-delay: var(--duration-200);
}

.animation-delay-300 {
  animation-delay: var(--duration-300);
}

.animation-delay-500 {
  animation-delay: var(--duration-500);
}

.animation-delay-700 {
  animation-delay: var(--duration-700);
}

.animation-delay-1000 {
  animation-delay: var(--duration-1000);
}

/* Animation duration utilities */
.animation-duration-75 {
  animation-duration: var(--duration-75);
}

.animation-duration-100 {
  animation-duration: var(--duration-100);
}

.animation-duration-150 {
  animation-duration: var(--duration-150);
}

.animation-duration-200 {
  animation-duration: var(--duration-200);
}

.animation-duration-300 {
  animation-duration: var(--duration-300);
}

.animation-duration-500 {
  animation-duration: var(--duration-500);
}

.animation-duration-700 {
  animation-duration: var(--duration-700);
}

.animation-duration-1000 {
  animation-duration: var(--duration-1000);
}

/* ===== HOVER ANIMATIONS ===== */

/* Hover scale */
.hover-scale {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Hover lift */
.hover-lift {
  transition: transform var(--duration-200) var(--ease-out), 
              box-shadow var(--duration-200) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* Hover glow */
.hover-glow {
  transition: box-shadow var(--duration-300) var(--ease-out);
}

.hover-glow:hover {
  box-shadow: 0 0 15px var(--color-primary-light);
}

/* Hover rotate */
.hover-rotate {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

/* ===== MICRO-INTERACTIONS ===== */

/* Button press effect */
.press-effect {
  transition: transform var(--duration-100) var(--ease-out);
}

.press-effect:active {
  transform: scale(0.97);
}

/* Ripple effect */
.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect .ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: ripple var(--duration-700) linear;
  pointer-events: none;
}

/* Focus ring animation */
.focus-ring-animation:focus-visible {
  animation: pulse var(--duration-1000) cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--color-primary);
  animation: spin var(--duration-700) linear infinite;
}

/* Loading dots */
.loading-dots::after {
  content: '.';
  animation: loading-dots var(--duration-1000) steps(4, end) infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60% {
    content: '...';
  }
  80%, 100% {
    content: '';
  }
}

/* Success checkmark animation */
.checkmark {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: block;
  stroke-width: 2;
  stroke: white;
  stroke-miterlimit: 10;
  box-shadow: inset 0 0 0 var(--color-success);
  animation: checkmark-fill var(--duration-300) ease-in-out forwards, 
             checkmark-scale var(--duration-300) ease-in-out var(--duration-300) both;
}

.checkmark-circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: var(--color-success);
  fill: none;
  animation: checkmark-stroke var(--duration-500) cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark-check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: checkmark-stroke var(--duration-300) cubic-bezier(0.65, 0, 0.45, 1) var(--duration-300) forwards;
}

@keyframes checkmark-stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes checkmark-scale {
  0%, 100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes checkmark-fill {
  100% {
    box-shadow: inset 0 0 0 30px var(--color-success);
  }
}

/* ===== STAGGERED ANIMATIONS ===== */

.stagger-fade-in > * {
  opacity: 0;
}

.stagger-fade-in > *:nth-child(1) {
  animation: fade-in var(--duration-500) var(--ease-out) forwards;
}

.stagger-fade-in > *:nth-child(2) {
  animation: fade-in var(--duration-500) var(--ease-out) var(--duration-100) forwards;
}

.stagger-fade-in > *:nth-child(3) {
  animation: fade-in var(--duration-500) var(--ease-out) var(--duration-200) forwards;
}

.stagger-fade-in > *:nth-child(4) {
  animation: fade-in var(--duration-500) var(--ease-out) var(--duration-300) forwards;
}

.stagger-fade-in > *:nth-child(5) {
  animation: fade-in var(--duration-500) var(--ease-out) var(--duration-400) forwards;
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  :root {
    --duration-75: var(--reduced-motion-duration);
    --duration-100: var(--reduced-motion-duration);
    --duration-150: var(--reduced-motion-duration);
    --duration-200: var(--reduced-motion-duration);
    --duration-300: var(--reduced-motion-duration);
    --duration-500: var(--reduced-motion-duration);
    --duration-700: var(--reduced-motion-duration);
    --duration-1000: var(--reduced-motion-duration);
  }
  
  .animate-fade-in,
  .animate-fade-out,
  .animate-slide-in-top,
  .animate-slide-in-bottom,
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-scale-up,
  .animate-scale-down,
  .animate-bounce,
  .animate-pulse,
  .animate-spin,
  .animate-ping,
  .animate-ripple,
  .animate-shimmer,
  .hover-scale:hover,
  .hover-lift:hover,
  .hover-glow:hover,
  .hover-rotate:hover,
  .press-effect:active,
  .ripple-effect .ripple,
  .focus-ring-animation:focus-visible,
  .loading-spinner,
  .loading-dots::after,
  .checkmark,
  .checkmark-circle,
  .checkmark-check,
  .stagger-fade-in > * {
    animation: none !important;
    transition: none !important;
    transform: none !important;
  }
}