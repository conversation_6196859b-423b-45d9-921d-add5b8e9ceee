# Design System Quick Reference

## Design Tokens

### Colors
```css
/* Primary */
--color-primary: #4f46e5
--color-primary-light: #6366f1
--color-primary-dark: #4338ca

/* Semantic */
--color-success: #10b981
--color-warning: #d97706
--color-error: #dc2626
--color-info: #2563eb

/* Surface */
--color-background: (theme-dependent)
--color-surface: (theme-dependent)
--color-text-primary: (theme-dependent)
--color-text-secondary: (theme-dependent)
```

### Spacing
```css
--space-1: 0.25rem  /* 4px */
--space-2: 0.5rem   /* 8px */
--space-3: 0.75rem  /* 12px */
--space-4: 1rem     /* 16px */
--space-5: 1.5rem   /* 24px */
--space-6: 2rem     /* 32px */
--space-8: 3rem     /* 48px */
```

### Typography
```css
--font-size-xs: 0.75rem   /* 12px */
--font-size-sm: 0.875rem  /* 14px */
--font-size-base: 1rem    /* 16px */
--font-size-lg: 1.125rem  /* 18px */
--font-size-xl: 1.25rem   /* 20px */
--font-size-2xl: 1.5rem   /* 24px */

--font-weight-normal: 400
--font-weight-medium: 500
--font-weight-semibold: 600
--font-weight-bold: 700
```

### Shadows
```css
--shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1)
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1)
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1)
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1)
```

### Border Radius
```css
--radius-sm: 0.125rem   /* 2px */
--radius-md: 0.375rem   /* 6px */
--radius-lg: 0.5rem     /* 8px */
--radius-xl: 0.75rem    /* 12px */
--radius-2xl: 1rem      /* 16px */
--radius-full: 9999px
```

## Component Classes

### Cards
```html
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Title</h3>
  </div>
  <div class="card-content">Content</div>
  <div class="card-footer">Footer</div>
</div>

<!-- Platform cards -->
<div class="card card-platform-twitter">...</div>
<div class="card card-platform-linkedin">...</div>
<div class="card card-platform-reddit">...</div>
```

### Buttons
```html
<button class="btn btn-primary">Primary</button>
<button class="btn btn-secondary">Secondary</button>
<button class="btn btn-ghost">Ghost</button>

<!-- Sizes -->
<button class="btn btn-primary btn-sm">Small</button>
<button class="btn btn-primary btn-lg">Large</button>

<!-- Icon button -->
<button class="btn btn-icon">
  <svg>...</svg>
</button>
```

### Forms
```html
<div class="form-group">
  <label class="form-label">Label</label>
  <input class="form-input" type="text">
  <div class="form-error">Error message</div>
</div>

<textarea class="form-textarea"></textarea>
<select class="form-select"></select>
```

### Navigation
```html
<nav class="nav">
  <a class="nav-item active">Active</a>
  <a class="nav-item">Item</a>
</nav>
```

### Badges
```html
<span class="badge badge-primary">Primary</span>
<span class="badge badge-success">Success</span>
<span class="badge badge-warning">Warning</span>
<span class="badge badge-error">Error</span>
```

### Alerts
```html
<div class="alert alert-success">Success message</div>
<div class="alert alert-warning">Warning message</div>
<div class="alert alert-error">Error message</div>
```

### Modals
```html
<div class="modal-backdrop show">
  <div class="modal show">
    <div class="modal-header">
      <h2 class="modal-title">Title</h2>
      <button class="modal-close">×</button>
    </div>
    <div class="modal-body">Content</div>
    <div class="modal-footer">
      <button class="btn btn-primary">Action</button>
    </div>
  </div>
</div>
```

## Layout Utilities

### Grid
```html
<div class="grid grid-cols-3 gap-4">...</div>
<div class="grid grid-auto-fit gap-6">...</div>

<!-- Responsive -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">...</div>
```

### Flexbox
```html
<div class="flex items-center justify-between">...</div>
<div class="flex flex-col gap-4">...</div>
```

### Container
```html
<div class="container">...</div>
<div class="container container-sm">...</div>
```

## Utility Classes

### Spacing
```html
<!-- Margin -->
<div class="m-4">All sides</div>
<div class="mx-4">Horizontal</div>
<div class="my-4">Vertical</div>
<div class="mt-4">Top</div>

<!-- Padding -->
<div class="p-4">All sides</div>
<div class="px-4">Horizontal</div>
<div class="py-4">Vertical</div>
```

### Typography
```html
<p class="text-xs">Extra small</p>
<p class="text-sm">Small</p>
<p class="text-base">Base</p>
<p class="text-lg">Large</p>

<p class="font-normal">Normal</p>
<p class="font-medium">Medium</p>
<p class="font-semibold">Semibold</p>
<p class="font-bold">Bold</p>

<p class="text-primary">Primary color</p>
<p class="text-secondary">Secondary color</p>
```

### Text Truncation
```html
<p class="truncate">Single line truncation</p>
<p class="line-clamp-2">Two line clamp</p>
<p class="line-clamp-3">Three line clamp</p>
```

### Colors
```html
<div class="bg-surface">Surface background</div>
<div class="bg-primary">Primary background</div>
<div class="text-primary">Primary text</div>
<div class="border border-primary">Primary border</div>
```

### Shadows
```html
<div class="shadow-sm">Small shadow</div>
<div class="shadow-md">Medium shadow</div>
<div class="shadow-lg">Large shadow</div>
```

### Border Radius
```html
<div class="rounded-sm">Small radius</div>
<div class="rounded-md">Medium radius</div>
<div class="rounded-lg">Large radius</div>
<div class="rounded-full">Full radius</div>
```

## Responsive Breakpoints

```css
/* Mobile: 0-767px (default) */
/* Tablet: 768px-1023px */
/* Desktop: 1024px+ */
```

### Responsive Classes
```html
<!-- Hide/show at breakpoints -->
<div class="hidden md:block">Hidden on mobile</div>
<div class="block md:hidden">Visible on mobile only</div>

<!-- Responsive grid -->
<div class="grid-cols-1 md:grid-cols-2 lg:grid-cols-3">...</div>

<!-- Responsive text -->
<h1 class="text-xl md:text-2xl lg:text-3xl">Responsive heading</h1>
```

## Platform Colors

```css
--color-twitter: #1a8cd8
--color-linkedin: #0a66c2
--color-reddit: #ff4500
--color-instagram: #c13584
--color-pinterest: #e60023
--color-web: #10b981
```

### Platform Classes
```html
<div class="platform-badge platform-badge-twitter">Twitter</div>
<div class="platform-icon platform-twitter">...</div>
<div class="platform-logo twitter"></div>
```

## Animation Classes

```html
<div class="transition-all">All properties</div>
<div class="transition-colors">Colors only</div>
<div class="transition-transform">Transform only</div>

<div class="hover:scale-105">Scale on hover</div>
<div class="hover:-translate-y-1">Lift on hover</div>
```

## Accessibility Classes

```html
<span class="sr-only">Screen reader only</span>
<a class="skip-link" href="#main">Skip to content</a>
```

## Common Patterns

### Card Grid
```html
<div class="grid grid-auto-fit gap-6">
  <div class="card">...</div>
  <div class="card">...</div>
  <div class="card">...</div>
</div>
```

### Header Layout
```html
<header class="flex items-center justify-between p-4 bg-surface border-b">
  <div class="flex items-center gap-3">
    <img src="logo.svg" class="h-8">
    <h1 class="text-xl font-semibold">Title</h1>
  </div>
  <nav class="flex items-center gap-4">
    <a href="#">Link</a>
    <button class="btn btn-primary">Action</button>
  </nav>
</header>
```

### Sidebar Layout
```html
<div class="flex min-h-screen">
  <aside class="w-64 bg-surface border-r">
    <!-- Sidebar content -->
  </aside>
  <main class="flex-1 p-6">
    <!-- Main content -->
  </main>
</div>
```

### Form Layout
```html
<form class="space-y-4">
  <div class="form-group">
    <label class="form-label">Label</label>
    <input class="form-input" type="text">
  </div>
  <div class="flex gap-3">
    <button type="button" class="btn btn-secondary">Cancel</button>
    <button type="submit" class="btn btn-primary">Submit</button>
  </div>
</form>
```

## Import Structure

```css
/* Main entry point */
@import './styles/optimized-index.css';

/* Or individual imports */
@import './styles/design-system/index.css';
@import './styles/core/base.css';
@import './styles/core/layout.css';
@import './styles/core/components.css';
@import './styles/core/utilities.css';
```

## Theme Usage

```html
<!-- Light theme -->
<html class="light-theme">

<!-- Dark theme -->
<html class="dark-theme">

<!-- Auto theme (system preference) -->
<html>
```

## Performance Tips

1. Use design tokens instead of hardcoded values
2. Prefer utility classes for simple styling
3. Use component classes for complex patterns
4. Minimize CSS specificity
5. Leverage browser caching with logical file structure
6. Test with reduced motion preferences
7. Ensure proper contrast ratios
8. Use semantic HTML elements