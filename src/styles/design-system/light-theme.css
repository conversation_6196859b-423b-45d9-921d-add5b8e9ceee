/* 
 * Light Theme
 * Defines color tokens specific to the light theme
 */

.light-theme {
  /* Semantic Colors */
  --color-background: var(--color-gray-50);
  --color-surface: white;
  --color-surface-hover: var(--color-gray-100);
  --color-border: var(--color-gray-200);
  --color-border-hover: var(--color-gray-300);
  --color-text-primary: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-700);
  --color-text-tertiary: var(--color-gray-500);
  
  /* Component-specific */
  --card-background: var(--color-surface);
  --card-border-color: var(--color-border);
  --card-shadow: var(--shadow-sm);
  --card-shadow-hover: var(--shadow-md);
  
  /* Light theme specific shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  
  /* Platform-specific colors */
  --platform-twitter-bg: rgba(29, 161, 242, 0.1);
  --platform-twitter-border: rgba(29, 161, 242, 0.3);
  --platform-linkedin-bg: rgba(0, 119, 181, 0.1);
  --platform-linkedin-border: rgba(0, 119, 181, 0.3);
  --platform-reddit-bg: rgba(255, 69, 0, 0.1);
  --platform-reddit-border: rgba(255, 69, 0, 0.3);
  --platform-instagram-bg: rgba(225, 48, 108, 0.1);
  --platform-instagram-border: rgba(225, 48, 108, 0.3);
  --platform-pinterest-bg: rgba(230, 0, 35, 0.1);
  --platform-pinterest-border: rgba(230, 0, 35, 0.3);
  --platform-web-bg: rgba(16, 185, 129, 0.1);
  --platform-web-border: rgba(16, 185, 129, 0.3);
}