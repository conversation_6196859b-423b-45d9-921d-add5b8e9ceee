/* 
 * Screen Reader Support for <PERSON><PERSON>
 * Enhances the interface for better screen reader accessibility
 */

/* ===== SCREEN READER ONLY CONTENT ===== */

/* Hide content visually but keep it accessible to screen readers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Make content visible when focused for keyboard-only users */
.sr-only-focusable:not(:focus) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* ===== ARIA ATTRIBUTES ===== */

/* Ensure proper ARIA attributes for common components */

/* Button states */
[role="button"][aria-pressed="true"] {
  background-color: var(--color-primary);
  color: white;
}

[role="button"][aria-disabled="true"] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Accordion */
[role="region"][aria-hidden="true"] {
  display: none;
}

/* Tabs */
[role="tab"][aria-selected="true"] {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

[role="tabpanel"][aria-hidden="true"] {
  display: none;
}

/* Dialog */
[role="dialog"][aria-hidden="true"],
[role="alertdialog"][aria-hidden="true"] {
  display: none;
}

/* Menu */
[role="menu"][aria-hidden="true"] {
  display: none;
}

/* Tooltip */
[role="tooltip"] {
  position: absolute;
  z-index: 10;
  padding: 0.5rem;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Alert */
[role="alert"] {
  position: relative;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-surface);
}

/* Status */
[role="status"] {
  position: relative;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-surface);
}

/* ===== SEMANTIC HTML ===== */

/* Ensure proper styling for semantic HTML elements */

/* Headings */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
}

/* Lists */
ul, ol {
  margin-top: 0;
  margin-bottom: 1rem;
  padding-left: 2rem;
}

/* Definition lists */
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

dt {
  font-weight: 600;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

/* Blockquote */
blockquote {
  margin: 0 0 1rem;
  padding: 0.5rem 1rem;
  border-left: 0.25rem solid var(--color-border);
}

/* Figure */
figure {
  margin: 0 0 1rem;
}

figcaption {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

/* Table */
table {
  width: 100%;
  margin-bottom: 1rem;
  border-collapse: collapse;
}

caption {
  padding: 0.5rem;
  caption-side: bottom;
  text-align: left;
  color: var(--color-text-secondary);
}

th {
  text-align: inherit;
  font-weight: 600;
}

th, td {
  padding: 0.5rem;
  vertical-align: top;
  border-top: 1px solid var(--color-border);
}

/* Form elements */
fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

/* ===== TEXT ALTERNATIVES ===== */

/* Ensure images have proper alt text */
img:not([alt]) {
  outline: 2px dashed var(--color-error);
  outline-offset: 2px;
}

/* Ensure SVGs are accessible */
svg[role="img"]:not([aria-label]) {
  outline: 2px dashed var(--color-error);
  outline-offset: 2px;
}

/* Ensure icons have proper text alternatives */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon svg {
  fill: currentColor;
}

/* ===== LIVE REGIONS ===== */

/* Ensure live regions are properly styled */
[aria-live="polite"],
[aria-live="assertive"] {
  position: relative;
}

/* Status messages */
.status-message {
  position: relative;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-surface);
}

.status-message[aria-live="polite"] {
  border-left: 0.25rem solid var(--color-info);
}

.status-message[aria-live="assertive"] {
  border-left: 0.25rem solid var(--color-error);
}

/* ===== FORM ACCESSIBILITY ===== */

/* Ensure form controls have proper labels */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
}

/* Ensure form controls have proper descriptions */
.form-text {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

/* Ensure form controls have proper error messages */
.form-error-message {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--color-error);
}

/* Ensure form controls are properly grouped */
.form-check {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.form-check-input {
  margin-right: 0.5rem;
  margin-top: 0.25rem;
}

.form-check-label {
  margin-bottom: 0;
}

/* ===== KEYBOARD NAVIGATION ===== */

/* Ensure all interactive elements are keyboard accessible */
[tabindex="-1"] {
  outline: none;
}

/* Ensure proper tab order */
.tab-order-first {
  order: -1;
}

.tab-order-last {
  order: 999;
}

/* ===== SKIP LINKS ===== */

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background-color: var(--color-primary);
  color: white;
  padding: 0.5rem 1rem;
  z-index: 100;
  transition: top 0.2s ease;
}

.skip-link:focus {
  top: 0;
}

/* ===== FOCUS MANAGEMENT ===== */

/* Focus outline styles */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 0.25rem;
}

/* High contrast focus styles */
@media (forced-colors: active) {
  :focus-visible {
    outline: 2px solid Highlight;
    outline-offset: 2px;
  }
}

/* Focus styles for interactive elements */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[role="button"]:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.2);
}

/* ===== REDUCED MOTION ===== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}