# Notely Design System Documentation

## Overview

The Notely Design System is a comprehensive collection of design tokens, components, and guidelines that ensure consistency and maintainability across the <PERSON>ly application. This documentation provides developers and designers with everything needed to implement and extend the design system.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Design Tokens](#design-tokens)
3. [Components](#components)
4. [Layout System](#layout-system)
5. [Typography](#typography)
6. [Color System](#color-system)
7. [Spacing](#spacing)
8. [Responsive Design](#responsive-design)
9. [Accessibility](#accessibility)
10. [Best Practices](#best-practices)
11. [Migration Guide](#migration-guide)

## Getting Started

### Installation

Import the design system in your main CSS file:

```css
@import './styles/design-system/index.css';
```

Or use the optimized version:

```css
@import './styles/optimized-index.css';
```

### Basic Usage

```html
<div class="card">
  <div class="card-header">
    <h2 class="card-title">Card Title</h2>
  </div>
  <div class="card-content">
    <p class="text-secondary">Card content goes here.</p>
  </div>
</div>
```

## Design Tokens

Design tokens are the foundation of the design system, providing consistent values for colors, spacing, typography, and other design properties.

### Color Tokens

```css
/* Primary Colors */
--color-primary: #4f46e5;
--color-primary-light: #6366f1;
--color-primary-dark: #4338ca;

/* Semantic Colors */
--color-success: #10b981;
--color-warning: #d97706;
--color-error: #dc2626;
--color-info: #2563eb;

/* Surface Colors */
--color-background: var(--color-background-dark);
--color-surface: var(--color-surface-dark);
--color-text-primary: var(--color-text-primary-dark);
```

### Usage Example

```css
.my-component {
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}
```

### Spacing Tokens

```css
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-3: 0.75rem;  /* 12px */
--space-4: 1rem;     /* 16px */
--space-5: 1.5rem;   /* 24px */
--space-6: 2rem;     /* 32px */
--space-8: 3rem;     /* 48px */
```

### Typography Tokens

```css
/* Font Sizes */
--font-size-xs: 0.75rem;   /* 12px */
--font-size-sm: 0.875rem;  /* 14px */
--font-size-base: 1rem;    /* 16px */
--font-size-lg: 1.125rem;  /* 18px */
--font-size-xl: 1.25rem;   /* 20px */

/* Font Weights */
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

## Components

### Card Component

The card component is used to display content in a structured, visually appealing container.

#### Basic Card

```html
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Card Title</h3>
  </div>
  <div class="card-content">
    <p>Card content goes here.</p>
  </div>
  <div class="card-footer">
    <button class="btn btn-primary">Action</button>
  </div>
</div>
```

#### Platform Card

```html
<div class="card card-platform-twitter">
  <div class="card-header">
    <img src="avatar.jpg" alt="User" class="card-avatar">
    <div>
      <h4 class="card-title">@username</h4>
      <span class="card-subtitle">Twitter</span>
    </div>
  </div>
  <div class="card-content">
    <p class="card-text">Tweet content here...</p>
  </div>
</div>
```

#### Card Variants

- `.card-platform-twitter` - Twitter-themed card
- `.card-platform-linkedin` - LinkedIn-themed card
- `.card-platform-reddit` - Reddit-themed card
- `.card-platform-instagram` - Instagram-themed card
- `.card-platform-pinterest` - Pinterest-themed card
- `.card-platform-web` - Web/generic-themed card

### Button Component

Buttons are used for actions and navigation throughout the application.

#### Button Variants

```html
<!-- Primary Button -->
<button class="btn btn-primary">Primary Action</button>

<!-- Secondary Button -->
<button class="btn btn-secondary">Secondary Action</button>

<!-- Ghost Button -->
<button class="btn btn-ghost">Ghost Action</button>

<!-- Icon Button -->
<button class="btn btn-icon">
  <svg>...</svg>
</button>
```

#### Button Sizes

```html
<button class="btn btn-primary btn-sm">Small</button>
<button class="btn btn-primary">Default</button>
<button class="btn btn-primary btn-lg">Large</button>
```

#### Button States

```html
<!-- Disabled -->
<button class="btn btn-primary" disabled>Disabled</button>

<!-- Loading -->
<button class="btn btn-primary loading">Loading...</button>
```

### Form Components

#### Input Fields

```html
<div class="form-group">
  <label class="form-label" for="email">Email</label>
  <input 
    type="email" 
    id="email" 
    class="form-input" 
    placeholder="Enter your email"
  >
</div>
```

#### Textarea

```html
<div class="form-group">
  <label class="form-label" for="message">Message</label>
  <textarea 
    id="message" 
    class="form-textarea" 
    placeholder="Enter your message"
  ></textarea>
</div>
```

#### Error States

```html
<div class="form-group">
  <label class="form-label" for="email">Email</label>
  <input 
    type="email" 
    id="email" 
    class="form-input error" 
    placeholder="Enter your email"
  >
  <div class="form-error">Please enter a valid email address.</div>
</div>
```

### Navigation Component

```html
<nav class="nav">
  <a href="#" class="nav-item active">
    <svg class="nav-item-icon">...</svg>
    All
  </a>
  <a href="#" class="nav-item">
    <svg class="nav-item-icon">...</svg>
    Twitter
  </a>
  <a href="#" class="nav-item">
    <svg class="nav-item-icon">...</svg>
    LinkedIn
  </a>
</nav>
```

### Modal Component

```html
<div class="modal-backdrop show">
  <div class="modal show">
    <div class="modal-header">
      <h2 class="modal-title">Modal Title</h2>
      <button class="modal-close">×</button>
    </div>
    <div class="modal-body">
      <p>Modal content goes here.</p>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary">Cancel</button>
      <button class="btn btn-primary">Confirm</button>
    </div>
  </div>
</div>
```

## Layout System

### Container System

```html
<div class="container">
  <!-- Content with max-width and centered -->
</div>

<div class="container container-sm">
  <!-- Smaller container -->
</div>
```

### Grid System

```html
<!-- Auto-fit grid -->
<div class="grid grid-auto-fit">
  <div class="card">Card 1</div>
  <div class="card">Card 2</div>
  <div class="card">Card 3</div>
</div>

<!-- Fixed columns -->
<div class="grid grid-cols-3 gap-4">
  <div>Column 1</div>
  <div>Column 2</div>
  <div>Column 3</div>
</div>
```

### Flexbox Utilities

```html
<div class="flex items-center justify-between">
  <div>Left content</div>
  <div>Right content</div>
</div>

<div class="flex flex-col gap-4">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</div>
```

## Typography

### Heading Hierarchy

```html
<h1 class="text-3xl font-bold">Main Heading</h1>
<h2 class="text-2xl font-semibold">Section Heading</h2>
<h3 class="text-xl font-medium">Subsection Heading</h3>
<h4 class="text-lg font-medium">Minor Heading</h4>
```

### Text Utilities

```html
<p class="text-primary">Primary text color</p>
<p class="text-secondary">Secondary text color</p>
<p class="text-tertiary">Tertiary text color</p>

<p class="text-sm">Small text</p>
<p class="text-base">Base text</p>
<p class="text-lg">Large text</p>

<p class="font-normal">Normal weight</p>
<p class="font-medium">Medium weight</p>
<p class="font-semibold">Semibold weight</p>
<p class="font-bold">Bold weight</p>
```

### Text Truncation

```html
<p class="truncate">This text will be truncated with ellipsis...</p>
<p class="line-clamp-2">This text will be clamped to 2 lines...</p>
<p class="line-clamp-3">This text will be clamped to 3 lines...</p>
```

## Color System

### Theme Colors

The design system supports both light and dark themes with automatic switching based on system preferences.

#### Light Theme

```css
.light-theme {
  --color-background: #ffffff;
  --color-surface: #ffffff;
  --color-text-primary: #111827;
  --color-text-secondary: #374151;
}
```

#### Dark Theme

```css
:root {
  --color-background: #121212;
  --color-surface: #1e1e1e;
  --color-text-primary: rgba(255, 255, 255, 0.95);
  --color-text-secondary: rgba(255, 255, 255, 0.8);
}
```

### Platform Colors

```css
--color-twitter: #1a8cd8;
--color-linkedin: #0a66c2;
--color-reddit: #ff4500;
--color-instagram: #c13584;
--color-pinterest: #e60023;
--color-web: #10b981;
```

### Usage Guidelines

- Use semantic color variables instead of hardcoded values
- Ensure sufficient contrast ratios (WCAG AA minimum)
- Test colors in both light and dark themes
- Use platform colors consistently for brand recognition

## Spacing

### Spacing Scale

The spacing system uses a consistent scale based on multiples of 4px:

```css
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-3: 0.75rem;  /* 12px */
--space-4: 1rem;     /* 16px */
--space-5: 1.5rem;   /* 24px */
--space-6: 2rem;     /* 32px */
--space-8: 3rem;     /* 48px */
--space-10: 4rem;    /* 64px */
--space-12: 6rem;    /* 96px */
```

### Spacing Utilities

```html
<!-- Margin -->
<div class="m-4">Margin on all sides</div>
<div class="mx-4">Horizontal margin</div>
<div class="my-4">Vertical margin</div>
<div class="mt-4">Top margin</div>

<!-- Padding -->
<div class="p-4">Padding on all sides</div>
<div class="px-4">Horizontal padding</div>
<div class="py-4">Vertical padding</div>
<div class="pt-4">Top padding</div>

<!-- Gap (for flex/grid) -->
<div class="flex gap-4">Items with gap</div>
<div class="grid gap-6">Grid with gap</div>
```

## Responsive Design

### Breakpoints

```css
/* Mobile: 0-767px (default) */
/* Tablet: 768px-1023px */
/* Desktop: 1024px+ */
```

### Responsive Utilities

```html
<!-- Hide on mobile -->
<div class="hidden sm:block">Visible on tablet and desktop</div>

<!-- Different layouts per breakpoint -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</div>

<!-- Responsive text sizes -->
<h1 class="text-xl md:text-2xl lg:text-3xl">Responsive heading</h1>
```

### Mobile-First Approach

Always design for mobile first, then enhance for larger screens:

```css
/* Mobile styles (default) */
.component {
  padding: var(--space-4);
  font-size: var(--font-size-sm);
}

/* Tablet and up */
@media (min-width: 768px) {
  .component {
    padding: var(--space-6);
    font-size: var(--font-size-base);
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  .component {
    padding: var(--space-8);
    font-size: var(--font-size-lg);
  }
}
```

## Accessibility

### Focus Management

```css
/* Visible focus indicators */
.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px var(--focus-ring-color);
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
```

### Screen Reader Support

```html
<!-- Screen reader only text -->
<span class="sr-only">Additional context for screen readers</span>

<!-- Proper labeling -->
<button aria-label="Close modal">×</button>

<!-- Semantic HTML -->
<nav aria-label="Main navigation">
  <ul>
    <li><a href="/">Home</a></li>
    <li><a href="/about">About</a></li>
  </ul>
</nav>
```

### Color Contrast

All color combinations meet WCAG AA standards:

- Normal text: 4.5:1 contrast ratio minimum
- Large text: 3:1 contrast ratio minimum
- Interactive elements: 3:1 contrast ratio minimum

### Reduced Motion

```css
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

## Best Practices

### CSS Organization

1. **Use design tokens**: Always use CSS custom properties instead of hardcoded values
2. **Follow naming conventions**: Use consistent, descriptive class names
3. **Maintain specificity**: Keep CSS selectors as simple as possible
4. **Group related styles**: Organize CSS logically within files

### Component Development

1. **Start with base styles**: Build components from design tokens
2. **Add variants carefully**: Only create variants that are actually needed
3. **Test responsiveness**: Ensure components work on all screen sizes
4. **Verify accessibility**: Test with keyboard navigation and screen readers

### Performance

1. **Minimize CSS**: Remove unused styles regularly
2. **Optimize selectors**: Avoid overly complex selectors
3. **Use efficient properties**: Prefer `transform` and `opacity` for animations
4. **Leverage caching**: Structure CSS for optimal browser caching

### Maintenance

1. **Document changes**: Update documentation when modifying components
2. **Test thoroughly**: Verify changes don't break existing functionality
3. **Review regularly**: Conduct periodic audits of the design system
4. **Gather feedback**: Collect input from developers and designers

## Migration Guide

### From Legacy Styles

If migrating from existing styles to the design system:

1. **Audit current styles**: Identify patterns and inconsistencies
2. **Map to design tokens**: Replace hardcoded values with tokens
3. **Consolidate components**: Merge similar components
4. **Update class names**: Use new naming conventions
5. **Test thoroughly**: Verify visual consistency

### Example Migration

```css
/* Before */
.old-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* After */
.card {
  background-color: var(--color-surface);
  border: var(--border-width-1) solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
}
```

### Breaking Changes

When introducing breaking changes:

1. **Document changes**: List all breaking changes clearly
2. **Provide migration path**: Show how to update existing code
3. **Deprecate gradually**: Give developers time to migrate
4. **Communicate early**: Announce changes well in advance

## Support and Resources

### Getting Help

- **Documentation**: This README and inline code comments
- **Examples**: Check the component demos and usage examples
- **Issues**: Report bugs or request features through the project's issue tracker

### Contributing

1. **Follow guidelines**: Adhere to the established patterns and conventions
2. **Test changes**: Verify your changes work across different scenarios
3. **Update documentation**: Keep documentation in sync with code changes
4. **Seek review**: Get feedback from other team members

### Tools and Resources

- **Design Tokens**: Figma design file with all tokens
- **Component Library**: Storybook with interactive examples
- **Accessibility Testing**: axe-core for automated accessibility testing
- **Performance Monitoring**: Lighthouse for performance audits

---

## Changelog

### Version 2.0.0 (Current)
- Complete redesign with new design tokens
- Improved accessibility features
- Better responsive design
- Performance optimizations
- Consolidated component library

### Version 1.0.0
- Initial design system implementation
- Basic component library
- Theme support
- Responsive utilities

---

For questions or support, please refer to the project documentation or contact the development team.