/* 
 * Design System
 * Main entry point for the design system
 */

/* Import design system tokens and theme files */
@import './design-system/index.css';

/* Import component-specific styles */
@import './theme-switcher.css';

/* 
 * Usage:
 * 
 * 1. Import this file in your main CSS or entry point:
 *    @import './styles/design-system.css';
 * 
 * 2. Initialize the theme in your JavaScript/TypeScript entry point:
 *    import { initializeTheme } from './utils/themeUtils';
 *    initializeTheme();
 * 
 * 3. Add the ThemeToggle component to your UI:
 *    import ThemeToggle from './components/ThemeToggle';
 *    <ThemeToggle />
 */