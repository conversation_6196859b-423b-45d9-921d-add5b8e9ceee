/* 
 * Form Elements for Notely
 * Enhanced form controls with proper visual feedback and accessibility
 */

/* ===== FORM CONTAINER ===== */

.form-container {
  width: 100%;
  max-width: 100%;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-col {
  flex: 1;
  min-width: 0;
}

/* ===== FORM LABELS ===== */

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-primary);
}

.form-label.required::after {
  content: '*';
  color: var(--color-error);
  margin-left: 0.25rem;
}

/* ===== FORM INPUTS ===== */

.form-input {
  display: block;
  width: 100%;
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:hover {
  border-color: var(--color-border-hover);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.form-input::placeholder {
  color: var(--color-text-tertiary);
  opacity: 1;
}

.form-input:disabled,
.form-input.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-surface-hover);
}

.form-input.error {
  border-color: var(--color-error);
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.form-input.success {
  border-color: var(--color-success);
}

.form-input.success:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* Input sizes */
.form-input-sm {
  padding: 0.375rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
}

.form-input-md {
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.5rem;
}

.form-input-lg {
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border-radius: 0.5rem;
}

/* ===== FORM TEXTAREA ===== */

.form-textarea {
  display: block;
  width: 100%;
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  resize: vertical;
  min-height: 100px;
}

.form-textarea:hover {
  border-color: var(--color-border-hover);
}

.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.form-textarea::placeholder {
  color: var(--color-text-tertiary);
  opacity: 1;
}

.form-textarea:disabled,
.form-textarea.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-surface-hover);
}

.form-textarea.error {
  border-color: var(--color-error);
}

.form-textarea.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.form-textarea.success {
  border-color: var(--color-success);
}

.form-textarea.success:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* ===== FORM SELECT ===== */

.form-select {
  display: block;
  width: 100%;
  padding: 0.625rem 2rem 0.625rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-surface);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  appearance: none;
}

.form-select:hover {
  border-color: var(--color-border-hover);
}

.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.form-select:disabled,
.form-select.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-surface-hover);
}

.form-select.error {
  border-color: var(--color-error);
}

.form-select.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.form-select.success {
  border-color: var(--color-success);
}

.form-select.success:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* Select sizes */
.form-select-sm {
  padding: 0.375rem 1.75rem 0.375rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
}

.form-select-md {
  padding: 0.625rem 2rem 0.625rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.5rem;
}

.form-select-lg {
  padding: 0.75rem 2.25rem 0.75rem 1rem;
  font-size: 1rem;
  border-radius: 0.5rem;
}

/* ===== FORM CHECKBOX & RADIO ===== */

.form-check {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.form-check-input {
  flex-shrink: 0;
  width: 1rem;
  height: 1rem;
  margin-top: 0.25rem;
  margin-right: 0.5rem;
  color: var(--color-primary);
  border: 1px solid var(--color-border);
  border-radius: 0.25rem;
  appearance: none;
  background-color: var(--color-surface);
  transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-check-input:checked {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23ffffff'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 0.75rem;
}

.form-check-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.form-check-input:disabled,
.form-check-input.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-surface-hover);
}

.form-check-input.error {
  border-color: var(--color-error);
}

.form-check-input.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.form-check-input.success {
  border-color: var(--color-success);
}

.form-check-input.success:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.form-check-label {
  font-size: 0.875rem;
  color: var(--color-text-primary);
}

/* Radio button */
.form-check-input[type="radio"] {
  border-radius: 50%;
}

.form-check-input[type="radio"]:checked {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3E%3Ccircle cx='8' cy='8' r='4' /%3E%3C/svg%3E");
  background-size: 0.75rem;
}

/* Switch */
.form-switch {
  padding-left: 2.5rem;
  position: relative;
}

.form-switch .form-check-input {
  position: absolute;
  left: 0;
  width: 2rem;
  height: 1rem;
  border-radius: 0.5rem;
  margin-top: 0.25rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23ffffff'%3E%3Ccircle cx='10' cy='10' r='8' /%3E%3C/svg%3E");
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 1rem;
  transition: background-position 0.2s ease;
}

.form-switch .form-check-input:checked {
  background-position: right center;
}

/* ===== FORM FEEDBACK ===== */

.form-text {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.form-error-message {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--color-error);
}

.form-success-message {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--color-success);
}

/* ===== FORM INPUT WITH ICON ===== */

.form-input-group {
  position: relative;
}

.form-input-icon-left {
  position: absolute;
  top: 50%;
  left: 0.75rem;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
  pointer-events: none;
}

.form-input-icon-right {
  position: absolute;
  top: 50%;
  right: 0.75rem;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
}

.form-input-with-icon-left {
  padding-left: 2.5rem;
}

.form-input-with-icon-right {
  padding-right: 2.5rem;
}

/* ===== FORM INPUT WITH CLEAR BUTTON ===== */

.form-input-clear {
  position: absolute;
  top: 50%;
  right: 0.75rem;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.form-input-clear:hover {
  background-color: var(--color-surface-hover);
}

.form-input-clear:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary);
}

/* ===== FORM LOADING STATE ===== */

.form-loading {
  position: relative;
}

.form-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0.75rem;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 2px solid var(--color-border);
  border-right-color: var(--color-primary);
  animation: spin 0.75s linear infinite;
}

@keyframes spin {
  from {
    transform: translateY(-50%) rotate(0deg);
  }
  to {
    transform: translateY(-50%) rotate(360deg);
  }
}

/* ===== FORM VALIDATION ICONS ===== */

.form-input-valid,
.form-input-invalid {
  position: relative;
}

.form-input-valid .form-input,
.form-input-valid .form-select,
.form-input-valid .form-textarea {
  padding-right: 2.5rem;
  border-color: var(--color-success);
}

.form-input-invalid .form-input,
.form-input-invalid .form-select,
.form-input-invalid .form-textarea {
  padding-right: 2.5rem;
  border-color: var(--color-error);
}

.form-input-valid::after,
.form-input-invalid::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0.75rem;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1rem;
}

.form-input-valid::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%2310b981'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.form-input-invalid::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23ef4444'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* ===== FORM ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .form-input,
  .form-textarea,
  .form-select,
  .form-check-input,
  .form-switch .form-check-input,
  .form-input-clear,
  .form-loading::after {
    transition: none;
  }
}

/* High contrast mode */
@media (forced-colors: active) {
  .form-input,
  .form-textarea,
  .form-select,
  .form-check-input {
    border: 1px solid ButtonText;
  }
  
  .form-input:focus,
  .form-textarea:focus,
  .form-select:focus,
  .form-check-input:focus {
    outline: 2px solid Highlight;
    border-color: Highlight;
  }
  
  .form-check-input:checked {
    background-color: Highlight;
    border-color: Highlight;
  }
}

/* Mobile optimizations */
@media (max-width: 767px) {
  .form-input,
  .form-textarea,
  .form-select {
    font-size: 16px; /* Prevent iOS zoom on focus */
    padding: 0.75rem;
  }
  
  .form-check-input {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  .form-switch .form-check-input {
    width: 2.5rem;
    height: 1.25rem;
  }
  
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .form-col {
    width: 100%;
  }
}