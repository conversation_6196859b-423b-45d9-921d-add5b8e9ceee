/* 
 * Mobile-Specific Optimizations for Notely
 * Enhances the mobile experience with touch-friendly targets and optimized layouts
 */

/* ===== MOBILE NAVIGATION OPTIMIZATIONS ===== */

@media (max-width: 767px) {
  /* Header and navigation container */
  .notely-header {
    padding: 0.75rem !important;
  }
  
  /* Platform tabs for mobile */
  .platform-tabs-container {
    margin-bottom: 1rem !important;
    padding-bottom: 0.5rem !important;
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    scrollbar-width: none !important; /* Firefox */
  }
  
  .platform-tabs-container::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari, Edge */
  }
  
  .platform-tab {
    min-height: 44px !important; /* Minimum touch target size */
    min-width: 44px !important;
    padding: 0.5rem 0.75rem !important;
    margin-right: 0.5rem !important;
  }
  
  /* Ensure platform icons are visible and properly sized */
  .platform-tab-icon {
    width: 20px !important;
    height: 20px !important;
  }
  
  /* Improve search bar for mobile */
  .search-container {
    width: 100% !important;
    margin: 0.5rem 0 !important;
  }
  
  .search-input {
    height: 44px !important; /* Minimum touch target size */
    font-size: 16px !important; /* Prevent iOS zoom on focus */
    padding-left: 2.5rem !important;
  }
  
  .search-icon {
    left: 0.75rem !important;
    width: 20px !important;
    height: 20px !important;
  }
  
  /* Improve filter chips for mobile */
  .filter-chip {
    min-height: 36px !important;
    padding: 0.5rem 0.75rem !important;
    margin-bottom: 0.5rem !important;
  }
  
  .filter-chip-remove {
    min-width: 24px !important;
    min-height: 24px !important;
  }
}

/* ===== MOBILE CARD OPTIMIZATIONS ===== */

@media (max-width: 767px) {
  /* Card spacing and layout */
  .posts-grid {
    gap: 1rem !important;
  }
  
  .post-card,
  article.post-card,
  .notely-card,
  article.notely-card {
    margin-bottom: 1rem !important;
  }
  
  /* Improve touch targets for all interactive elements */
  .post-card button,
  .post-card a,
  article.post-card button,
  article.post-card a,
  .notely-card button,
  .notely-card a {
    min-width: 44px !important;
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  
  /* Improve spacing between action buttons */
  .post-card .flex.items-center.space-x-2,
  article.post-card .flex.items-center.space-x-2 {
    gap: 0.5rem !important;
  }
  
  /* Ensure text is readable on small screens */
  .post-card .post-content,
  article.post-card .post-content {
    font-size: 0.9375rem !important;
    line-height: 1.5 !important;
    padding: 0.75rem 1rem !important;
  }
  
  /* Optimize footer actions */
  .post-card .notely-post-footer > div,
  article.post-card .notely-post-footer > div {
    padding: 0.5rem !important;
  }
  
  /* Optimize interaction metrics spacing */
  .post-card .flex.items-center.space-x-4,
  article.post-card .flex.items-center.space-x-4 {
    gap: 1rem !important;
  }
}

/* ===== MOBILE TOUCH OPTIMIZATIONS ===== */

@media (hover: none) {
  /* Provide better touch feedback */
  .post-card button:active,
  .post-card a:active,
  article.post-card button:active,
  article.post-card a:active,
  .platform-tab:active,
  .filter-chip:active {
    transform: scale(0.96) !important;
    opacity: 0.8 !important;
    transition: transform 0.1s ease-out, opacity 0.1s ease-out !important;
  }
  
  /* Remove hover effects that might cause confusion on touch devices */
  .post-card:hover,
  article.post-card:hover,
  .platform-tab:hover,
  .filter-chip:hover {
    transform: none !important;
    box-shadow: none !important;
  }
  
  /* Ensure active states are visible */
  .platform-tab.active,
  .filter-chip.active {
    transform: none !important;
    box-shadow: var(--shadow-md) !important;
  }
}

/* ===== MOBILE LAYOUT OPTIMIZATIONS ===== */

@media (max-width: 767px) {
  /* Optimize main container padding */
  .posts-container {
    padding: 0.75rem !important;
  }
  
  /* Optimize header layout */
  .notely-header {
    flex-direction: column !important;
    align-items: stretch !important;
  }
  
  .notely-header-actions {
    margin-top: 0.5rem !important;
    justify-content: space-between !important;
  }
  
  /* Optimize filter sections */
  .filter-section {
    margin-bottom: 1rem !important;
  }
  
  .filter-section-header {
    flex-direction: column !important;
    align-items: flex-start !important;
  }
  
  .filter-section-actions {
    margin-top: 0.5rem !important;
    width: 100% !important;
    justify-content: flex-end !important;
  }
  
  /* Optimize active filters display */
  .active-filters {
    padding: 0.5rem !important;
    margin-bottom: 1rem !important;
  }
}

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */

@media (max-width: 767px) {
  /* Reduce animation complexity for better performance */
  .post-card,
  .platform-tab,
  .filter-chip,
  .post-card button,
  .post-card a {
    transition-duration: 0.15s !important;
  }
  
  /* Disable backdrop filters on lower-end devices for better performance */
  @supports not (backdrop-filter: blur(10px)) {
    .post-card .notely-post-footer > div,
    article.post-card .notely-post-footer > div,
    .post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full {
      backdrop-filter: none !important;
      background-color: rgba(24, 24, 24, 0.95) !important;
    }
  }
}