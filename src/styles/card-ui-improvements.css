/* 
 * Card UI Improvements for Notely
 * Enhanced card design for better visual clarity, readability, and usability
 * in the dark-themed social media content aggregator
 */

/* ===== CARD BASE STYLES ===== */

.post-card,
article.post-card,
.notely-card,
article.notely-card {
  /* Improved background contrast */
  background-color: #1D1D1D;
  
  /* Enhanced border and shadow */
  border: 1px solid #2F2F2F;
  border-radius: 12px;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
  
  /* Improved spacing */
  margin-bottom: 24px;
  overflow: hidden;
  
  /* Smooth transitions */
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
}

.post-card:hover,
article.post-card:hover,
.notely-card:hover,
article.notely-card:hover {
  transform: translateY(-8px);
  box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.3);
  border-color: #3F3F3F;
}

/* ===== CARD HEADER STYLES ===== */

.post-card .flex.items-center.space-x-3,
.notely-card .flex.items-center.space-x-3 {
  padding: 18px 24px 14px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* Author name styling */
.post-card .text-sm.font-medium.text-notely-text-primary,
.notely-card .text-sm.font-medium.text-notely-text-primary {
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 4px;
}

/* Author handle styling */
.post-card .text-xs.text-notely-text-muted,
.notely-card .text-xs.text-notely-text-muted {
  font-size: 14px;
  color: #A0A0A0;
}

/* Timestamp styling */
.post-card .text-xs.text-notely-text-muted + .text-xs.text-notely-text-muted,
.notely-card .text-xs.text-notely-text-muted + .text-xs.text-notely-text-muted {
  font-size: 12px;
  color: #808080;
  margin-top: 2px;
}

/* ===== CARD CONTENT STYLES ===== */

/* Content padding */
.post-card .px-4.pb-3,
.notely-card .px-4.pb-3 {
  padding: 18px 24px 16px;
}

/* Text content styling */
.post-card .text-sm.text-notely-text-secondary,
.notely-card .text-sm.text-notely-text-secondary {
  font-size: 15px;
  line-height: 1.5;
  color: #EAEAEA;
  margin-bottom: 16px;
}

/* Media content styling */
.post-card .relative.rounded-lg.overflow-hidden,
.notely-card .relative.rounded-lg.overflow-hidden {
  border-radius: 8px;
  overflow: hidden;
  margin-top: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Image styling */
.post-card .w-full.h-32.object-cover,
.notely-card .w-full.h-32.object-cover {
  height: 180px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.post-card .relative.rounded-lg.overflow-hidden:hover .w-full.h-32.object-cover,
.notely-card .relative.rounded-lg.overflow-hidden:hover .w-full.h-32.object-cover {
  transform: scale(1.03);
}

/* ===== CARD FOOTER STYLES ===== */

/* Footer container */
.post-card .px-4.pb-4,
.notely-card .px-4.pb-4 {
  padding: 16px 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

/* Metrics container */
.post-card .flex.items-center.justify-between,
.notely-card .flex.items-center.justify-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Metrics items */
.post-card .flex.items-center.space-x-4,
.notely-card .flex.items-center.space-x-4 {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Individual metric */
.post-card .flex.items-center.space-x-1,
.notely-card .flex.items-center.space-x-1 {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #A0A0A0;
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.post-card .flex.items-center.space-x-1:hover,
.notely-card .flex.items-center.space-x-1:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

/* Icon styling */
.post-card .w-3.h-3,
.notely-card .w-3.h-3 {
  width: 16px;
  height: 16px;
}

/* ===== TAGS STYLING ===== */

/* Tags container */
.post-card .flex.flex-wrap.gap-1,
.notely-card .flex.flex-wrap.gap-1 {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
}

/* Individual tag */
.post-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full,
.notely-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full {
  padding: 4px 12px;
  background-color: #2A2A2A;
  border-radius: 16px;
  font-size: 12px;
  color: #CCCCCC;
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
}

.post-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full:hover,
.notely-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full:hover {
  background-color: #3A3A3A;
  color: #FFFFFF;
  transform: translateY(-2px);
}

/* ===== BUTTON STYLING ===== */

/* Action buttons container */
.post-card .action-buttons,
.notely-card .action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Individual action button */
.post-card .action-button,
.notely-card .action-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #2A2A2A;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #CCCCCC;
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
  position: relative;
}

.post-card .action-button:hover,
.notely-card .action-button:hover {
  background-color: #3A3A3A;
  color: #FFFFFF;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Button tooltip */
.post-card button[title]::after,
.notely-card button[title]::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  background-color: #000000;
  color: #FFFFFF;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease, transform 0.2s ease;
  z-index: 10;
}

.post-card button[title]:hover::after,
.notely-card button[title]:hover::after {
  opacity: 1;
  transform: translateX(-50%) translateY(-4px);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 1024px) {
  .post-card .flex.items-center.space-x-3,
  .notely-card .flex.items-center.space-x-3,
  .post-card .px-4.pb-3,
  .notely-card .px-4.pb-3,
  .post-card .px-4.pb-4,
  .notely-card .px-4.pb-4 {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 768px) {
  .post-card .flex.items-center.space-x-3,
  .notely-card .flex.items-center.space-x-3,
  .post-card .px-4.pb-3,
  .notely-card .px-4.pb-3,
  .post-card .px-4.pb-4,
  .notely-card .px-4.pb-4 {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .post-card .action-button,
  .notely-card .action-button {
    width: 44px;
    height: 44px;
  }
  
  .post-card .w-full.h-32.object-cover,
  .notely-card .w-full.h-32.object-cover {
    height: 160px;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

@media (prefers-reduced-motion: reduce) {
  .post-card,
  .notely-card,
  .post-card:hover,
  .notely-card:hover,
  .post-card .w-full.h-32.object-cover,
  .notely-card .w-full.h-32.object-cover,
  .post-card .action-button,
  .notely-card .action-button,
  .post-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full,
  .notely-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full {
    transition: none;
  }
  
  .post-card:hover,
  .notely-card:hover,
  .post-card .relative.rounded-lg.overflow-hidden:hover .w-full.h-32.object-cover,
  .notely-card .relative.rounded-lg.overflow-hidden:hover .w-full.h-32.object-cover,
  .post-card .action-button:hover,
  .notely-card .action-button:hover,
  .post-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full:hover,
  .notely-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full:hover {
    transform: none;
  }
}

.post-card .action-button:focus-visible,
.notely-card .action-button:focus-visible {
  outline: 2px solid #4F46E5;
  outline-offset: 2px;
}

/* High contrast mode */
@media (forced-colors: active) {
  .post-card,
  .notely-card {
    border: 2px solid ButtonText;
  }
  
  .post-card:hover,
  .notely-card:hover {
    border-color: Highlight;
  }
  
  .post-card .action-button,
  .notely-card .action-button {
    border: 1px solid ButtonText;
  }
  
  .post-card .action-button:hover,
  .notely-card .action-button:hover {
    border-color: Highlight;
  }
}

/* ===== BROWSER-SPECIFIC ADJUSTMENTS ===== */

/* Firefox-specific adjustments */
@-moz-document url-prefix() {
  .post-card,
  .notely-card {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  }
  
  .post-card .text-sm.text-notely-text-secondary,
  .notely-card .text-sm.text-notely-text-secondary {
    /* Firefox needs explicit line-clamp */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-clamp: 3;
  }
}

/* Safari-specific adjustments */
@media not all and (min-resolution:.001dpcm) { 
  @supports (-webkit-appearance:none) {
    .post-card,
    .notely-card {
      /* Safari-specific shadow adjustment */
      -webkit-box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    .post-card:hover,
    .notely-card:hover {
      -webkit-box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.3);
    }
    
    /* Safari needs explicit line-clamp */
    .post-card .text-sm.text-notely-text-secondary,
    .notely-card .text-sm.text-notely-text-secondary {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}

/* Edge/IE adjustments */
@supports (-ms-ime-align:auto) {
  .post-card,
  .notely-card {
    /* Edge-specific shadow adjustment */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

/* Ensure proper display on older browsers */
.post-card,
.notely-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.post-card .flex,
.notely-card .flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.post-card .items-center,
.notely-card .items-center {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.post-card .justify-between,
.notely-card .justify-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
/
* ===== FINAL ADJUSTMENTS AND FIXES ===== */

/* Fix for potential z-index issues with tooltips */
.post-card button[title]::after,
.notely-card button[title]::after {
  z-index: 100;
}

/* Fix for potential overflow issues with long text */
.post-card .text-sm.font-medium.text-notely-text-primary,
.notely-card .text-sm.font-medium.text-notely-text-primary,
.post-card .text-xs.text-notely-text-muted,
.notely-card .text-xs.text-notely-text-muted {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* Fix for potential issues with nested flex containers */
.post-card .flex.items-center.space-x-3 > div,
.notely-card .flex.items-center.space-x-3 > div {
  min-width: 0; /* Allow flex items to shrink below content size */
}

/* Fix for potential issues with image aspect ratios */
.post-card .w-full.h-32.object-cover,
.notely-card .w-full.h-32.object-cover {
  object-position: center; /* Ensure images are centered */
}

/* Fix for potential issues with button alignment in Firefox */
@-moz-document url-prefix() {
  .post-card .flex.items-center.justify-between,
  .notely-card .flex.items-center.justify-between {
    width: 100%; /* Ensure full width in Firefox */
  }
}

/* Fix for potential issues with dark mode detection */
@media (prefers-color-scheme: dark) {
  .post-card,
  .notely-card {
    /* Ensure proper contrast in system dark mode */
    background-color: #1D1D1D !important;
    border-color: #2F2F2F !important;
  }
}

/* Fix for potential issues with touch devices */
@media (hover: none) {
  .post-card:hover,
  .notely-card:hover {
    /* Disable hover effects on touch devices */
    transform: none;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
    border-color: #2F2F2F;
  }
  
  .post-card button[title]:hover::after,
  .notely-card button[title]:hover::after {
    /* Disable tooltips on touch devices */
    display: none;
  }
  
  .post-card .action-button:active,
  .notely-card .action-button:active {
    /* Add active state for touch devices */
    background-color: #3A3A3A;
    color: #FFFFFF;
  }
}

/* Fix for potential issues with print media */
@media print {
  .post-card,
  .notely-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
  }
  
  .post-card .text-sm.text-notely-text-secondary,
  .notely-card .text-sm.text-notely-text-secondary {
    color: #000 !important;
  }
}