/* 
 * Mobile Navigation Styles for Notely
 * Enhances the mobile navigation experience with touch-friendly controls
 */

/* ===== MOBILE NAVIGATION DRAWER ===== */

.mobile-nav-toggle {
  display: none;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.mobile-nav-toggle:active {
  transform: scale(0.95);
  background-color: var(--color-surface-hover);
}

.mobile-nav-drawer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-background);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
  z-index: 999;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  max-height: 70vh;
  overflow-y: auto;
}

.mobile-nav-drawer.open {
  transform: translateY(0);
}

.mobile-nav-content {
  padding: 20px 16px;
}

/* ===== MOBILE PLATFORM TABS ===== */

.mobile-platform-tabs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-platform-tab {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 12px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  transition: background-color 0.2s ease, transform 0.2s ease;
  width: 100%;
  text-align: left;
}

.mobile-platform-tab.active {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.mobile-platform-tab:active {
  transform: scale(0.98);
}

.mobile-platform-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  flex-shrink: 0;
}

.mobile-platform-name {
  font-weight: 500;
  flex-grow: 1;
}

.mobile-platform-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.1);
  color: inherit;
}

/* Platform-specific styling */
.mobile-platform-tab[data-platform="twitter"] {
  border-color: var(--color-twitter);
}

.mobile-platform-tab[data-platform="twitter"].active {
  background-color: var(--color-twitter);
  border-color: var(--color-twitter);
}

.mobile-platform-tab[data-platform="linkedin"] {
  border-color: var(--color-linkedin);
}

.mobile-platform-tab[data-platform="linkedin"].active {
  background-color: var(--color-linkedin);
  border-color: var(--color-linkedin);
}

.mobile-platform-tab[data-platform="reddit"] {
  border-color: var(--color-reddit);
}

.mobile-platform-tab[data-platform="reddit"].active {
  background-color: var(--color-reddit);
  border-color: var(--color-reddit);
}

.mobile-platform-tab[data-platform="instagram"] {
  border-color: var(--color-instagram);
}

.mobile-platform-tab[data-platform="instagram"].active {
  background-color: var(--color-instagram);
  border-color: var(--color-instagram);
}

.mobile-platform-tab[data-platform="pinterest"] {
  border-color: var(--color-pinterest);
}

.mobile-platform-tab[data-platform="pinterest"].active {
  background-color: var(--color-pinterest);
  border-color: var(--color-pinterest);
}

.mobile-platform-tab[data-platform="web"] {
  border-color: var(--color-web);
}

.mobile-platform-tab[data-platform="web"].active {
  background-color: var(--color-web);
  border-color: var(--color-web);
}

/* ===== MOBILE SEARCH ===== */

.mobile-search-container {
  margin: 16px 0;
  position: relative;
}

.mobile-search-input {
  width: 100%;
  height: 48px;
  padding: 0 16px 0 44px;
  border-radius: 24px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  font-size: 16px; /* Prevent iOS zoom on focus */
}

.mobile-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--color-text-secondary);
  pointer-events: none;
}

/* ===== MOBILE FILTER CHIPS ===== */

.mobile-filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 16px 0;
}

.mobile-filter-chip {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 16px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.mobile-filter-chip.active {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.mobile-filter-chip:active {
  transform: scale(0.95);
}

/* ===== RESPONSIVE DISPLAY ===== */

@media (min-width: 768px) {
  .mobile-nav-toggle,
  .mobile-nav-drawer {
    display: none;
  }
}

@media (max-width: 767px) {
  .mobile-nav-toggle {
    display: flex;
  }
  
  /* Hide desktop navigation on mobile */
  .desktop-only-nav {
    display: none;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .mobile-nav-toggle,
  .mobile-nav-drawer,
  .mobile-platform-tab {
    transition: none;
  }
  
  .mobile-platform-tab:active,
  .mobile-nav-toggle:active {
    transform: none;
  }
}

/* High contrast mode */
@media (forced-colors: active) {
  .mobile-nav-toggle {
    border: 2px solid ButtonText;
  }
  
  .mobile-platform-tab {
    border: 2px solid ButtonText;
  }
  
  .mobile-platform-tab.active {
    border: 2px solid HighlightText;
    background-color: Highlight;
    color: HighlightText;
  }
}