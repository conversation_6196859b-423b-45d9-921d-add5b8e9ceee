/* 
 * Card UI Overrides for Notely
 * These styles use higher specificity and !important to override Tailwind classes
 */

/* Card Container */
article.post-card.notely-card {
  background-color: #1D1D1D !important;
  border: 1px solid #2F2F2F !important;
  border-radius: 12px !important;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2) !important;
  margin-bottom: 24px !important;
  overflow: hidden !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease !important;
}

article.post-card.notely-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.3) !important;
  border-color: #3F3F3F !important;
}

/* Card Header */
article.post-card.notely-card .notely-breathing-compact-md,
article.post-card.notely-card div[class*="flex items-center"] {
  padding: 18px 24px 14px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* Author Name */
article.post-card.notely-card .author-name,
article.post-card.notely-card .text-lg {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #FFFFFF !important;
  margin-bottom: 4px !important;
}

/* Author Handle & Timestamp */
article.post-card.notely-card .author-handle,
article.post-card.notely-card .text-xs.text-notely-text-muted {
  font-size: 14px !important;
  color: #A0A0A0 !important;
}

article.post-card.notely-card .timestamp,
article.post-card.notely-card .text-xs.text-notely-text-muted + .text-xs.text-notely-text-muted {
  font-size: 12px !important;
  color: #808080 !important;
}

/* Card Content */
article.post-card.notely-card .px-4.pb-3,
article.post-card.notely-card .post-content {
  padding: 18px 24px 16px !important;
}

article.post-card.notely-card .text-sm.text-notely-text-secondary,
article.post-card.notely-card .text-[15px] {
  font-size: 15px !important;
  line-height: 1.5 !important;
  color: #EAEAEA !important;
  margin-bottom: 16px !important;
}

/* Media Content */
article.post-card.notely-card .relative.rounded-lg.overflow-hidden {
  border-radius: 8px !important;
  overflow: hidden !important;
  margin-top: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

article.post-card.notely-card .w-full.h-32.object-cover {
  height: 180px !important;
  object-fit: cover !important;
  transition: transform 0.3s ease !important;
}

article.post-card.notely-card .relative.rounded-lg.overflow-hidden:hover .w-full.h-32.object-cover {
  transform: scale(1.03) !important;
}

/* Card Footer */
article.post-card.notely-card .notely-post-footer > div {
  padding: 16px 24px !important;
  border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
  background-color: rgba(29, 29, 29, 0.95) !important;
}

/* Metrics */
article.post-card.notely-card .flex.items-center.space-x-4 {
  gap: 16px !important;
}

article.post-card.notely-card .flex.items-center.space-x-1.5,
article.post-card.notely-card .group.flex.items-center.space-x-1.5 {
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  color: #A0A0A0 !important;
  font-size: 13px !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease, color 0.2s ease !important;
}

article.post-card.notely-card .flex.items-center.space-x-1.5:hover,
article.post-card.notely-card .group.flex.items-center.space-x-1.5:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
  color: #FFFFFF !important;
}

/* Action Buttons */
article.post-card.notely-card .flex.items-center.space-x-2 {
  gap: 12px !important;
}

article.post-card.notely-card button[class*="p-2 rounded-lg"],
article.post-card.notely-card a[class*="p-2 rounded-lg"] {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  background-color: #2A2A2A !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #CCCCCC !important;
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease !important;
  position: relative !important;
}

article.post-card.notely-card button[class*="p-2 rounded-lg"]:hover,
article.post-card.notely-card a[class*="p-2 rounded-lg"]:hover {
  background-color: #3A3A3A !important;
  color: #FFFFFF !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Button Icons */
article.post-card.notely-card button[class*="p-2 rounded-lg"] .w-4.h-4,
article.post-card.notely-card a[class*="p-2 rounded-lg"] .w-4.h-4 {
  width: 18px !important;
  height: 18px !important;
}

/* Tags */
article.post-card.notely-card .flex.flex-wrap.gap-1 {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px !important;
  margin-top: 16px !important;
}

article.post-card.notely-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full {
  padding: 4px 12px !important;
  background-color: #2A2A2A !important;
  border-radius: 16px !important;
  font-size: 12px !important;
  color: #CCCCCC !important;
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease !important;
}

article.post-card.notely-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full:hover {
  background-color: #3A3A3A !important;
  color: #FFFFFF !important;
  transform: translateY(-2px) !important;
}

/* Platform Indicators */
article.post-card.notely-card[class*="border-t-[3px]"] {
  border-top-width: 6px !important;
}

/* Tooltips for buttons */
article.post-card.notely-card button[title]::after,
article.post-card.notely-card a[title]::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  background-color: #000000;
  color: #FFFFFF;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease, transform 0.2s ease;
  z-index: 100;
}

article.post-card.notely-card button[title]:hover::after,
article.post-card.notely-card a[title]:hover::after {
  opacity: 1;
  transform: translateX(-50%) translateY(-4px);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  article.post-card.notely-card,
  article.post-card.notely-card:hover,
  article.post-card.notely-card .w-full.h-32.object-cover,
  article.post-card.notely-card button[class*="p-2 rounded-lg"],
  article.post-card.notely-card a[class*="p-2 rounded-lg"],
  article.post-card.notely-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full {
    transition: none !important;
  }
  
  article.post-card.notely-card:hover,
  article.post-card.notely-card .relative.rounded-lg.overflow-hidden:hover .w-full.h-32.object-cover,
  article.post-card.notely-card button[class*="p-2 rounded-lg"]:hover,
  article.post-card.notely-card a[class*="p-2 rounded-lg"]:hover,
  article.post-card.notely-card .px-2.py-1.bg-notely-surface.text-notely-text-secondary.text-xs.rounded-full:hover {
    transform: none !important;
  }
}

article.post-card.notely-card button[class*="p-2 rounded-lg"]:focus-visible,
article.post-card.notely-card a[class*="p-2 rounded-lg"]:focus-visible {
  outline: 2px solid #4F46E5 !important;
  outline-offset: 2px !important;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
  article.post-card.notely-card .notely-breathing-compact-md,
  article.post-card.notely-card div[class*="flex items-center"] {
    padding: 16px 20px 12px !important;
  }
  
  article.post-card.notely-card .px-4.pb-3,
  article.post-card.notely-card .post-content {
    padding: 16px 20px 14px !important;
  }
  
  article.post-card.notely-card .notely-post-footer > div {
    padding: 14px 20px !important;
  }
  
  article.post-card.notely-card .author-name,
  article.post-card.notely-card .text-lg {
    font-size: 15px !important;
  }
  
  article.post-card.notely-card .w-full.h-32.object-cover {
    height: 160px !important;
  }
}