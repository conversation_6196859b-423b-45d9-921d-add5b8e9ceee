/* 
 * Responsive Grid System for Notely
 * Provides flexible grid layouts that adapt to different screen sizes
 * and override inline styles with higher specificity
 */

/* ===== GRID CONTAINER ===== */

.posts-container {
  width: 100% !important;
  max-width: 1400px !important;
  margin: 0 auto !important;
  padding: var(--space-4) !important;
}

.posts-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)) !important;
  gap: var(--space-6) !important;
  width: 100% !important;
  align-items: start !important;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
  .posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr)) !important;
    gap: var(--space-8) !important;
  }
  
  .posts-container {
    padding: var(--space-6) !important;
  }
}

/* Desktop (1024px - 1399px) */
@media (max-width: 1399px) and (min-width: 1024px) {
  .posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)) !important;
    gap: var(--space-5) !important;
  }
}

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
    gap: var(--space-4) !important;
  }
  
  .posts-container {
    padding: var(--space-3) !important;
  }
}

/* Mobile Large (640px - 767px) */
@media (max-width: 767px) and (min-width: 640px) {
  .posts-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-4) !important;
  }
  
  .posts-container {
    padding: var(--space-3) !important;
  }
}

/* Mobile Small (below 640px) */
@media (max-width: 639px) {
  .posts-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-3) !important;
  }
  
  .posts-container {
    padding: var(--space-2) !important;
  }
}

/* ===== CARD OVERRIDES TO HANDLE INLINE STYLES ===== */

/* Force override inline styles with higher specificity */
.post-card.notely-card[style],
article.post-card.notely-card[style] {
  background: linear-gradient(135deg, rgb(30, 30, 30) 0%, rgb(24, 24, 24) 50%, rgb(22, 22, 22) 100%) !important;
  border-radius: 1.25rem !important;
  box-shadow: rgba(0, 0, 0, 0.4) 0px 15px 35px, rgba(255, 255, 255, 0.08) 0px 0px 0px 1px !important;
  transition: transform 0.3s ease-out, box-shadow 0.3s ease-out, border-color 0.3s ease-out !important;
  border: 1px solid #2F2F2F !important;
  border-top: 6px solid #1da1f2 !important;
  overflow: hidden !important;
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
  height: auto !important;
  min-height: 200px !important;
}

/* Enhanced hover effects */
.post-card.notely-card[style]:hover,
article.post-card.notely-card[style]:hover {
  transform: translateY(-8px) !important;
  box-shadow: rgba(0, 0, 0, 0.5) 0px 20px 45px, rgba(255, 255, 255, 0.12) 0px 0px 0px 1px !important;
  border-color: rgba(99, 102, 241, 0.8) !important;
}

/* Platform-specific border colors */
.post-card.notely-card[data-post-id*="twitter"] {
  border-top-color: #1da1f2 !important;
}

.post-card.notely-card[data-post-id*="linkedin"] {
  border-top-color: #0077b5 !important;
}

.post-card.notely-card[data-post-id*="reddit"] {
  border-top-color: #ff4500 !important;
}

.post-card.notely-card[data-post-id*="instagram"] {
  border-top-color: #e1306c !important;
}

.post-card.notely-card[data-post-id*="pinterest"] {
  border-top-color: #e60023 !important;
}

/* ===== CARD CONTENT LAYOUT ===== */

.post-card .notely-breathing-compact-md {
  padding: 1.5rem 1.5rem 1rem !important;
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

/* Header section */
.post-card .flex.items-start.mb-2 {
  margin-bottom: 1rem !important;
  align-items: flex-start !important;
}

/* Avatar improvements */
.post-card .w-8.h-8,
.post-card .sm\\:w-10.sm\\:h-10 {
  width: 48px !important;
  height: 48px !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  transition: transform 0.3s ease !important;
}

.post-card:hover .w-8.h-8,
.post-card:hover .sm\\:w-10.sm\\:h-10 {
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

/* Author name styling */
.post-card .author-name,
.post-card .font-bold.text-lg {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: rgba(255, 255, 255, 0.95) !important;
  letter-spacing: -0.01em !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  margin-bottom: 0.25rem !important;
}

/* Author handle and timestamp */
.post-card .author-handle,
.post-card .text-notely-text-muted {
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 500 !important;
}

.post-card .timestamp {
  color: rgba(255, 255, 255, 0.6) !important;
  transition: color 0.2s ease !important;
}

.post-card .timestamp:hover {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Post content styling */
.post-card .post-content {
  padding: 1rem 1.5rem 1.5rem !important;
  font-size: 1.0625rem !important;
  line-height: 1.6 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  letter-spacing: -0.01em !important;
  flex: 1 !important;
}

/* Footer styling */
.post-card .mt-3.pt-3.pb-2,
.post-card .notely-post-footer > div {
  padding: 1rem 1.5rem !important;
  background: linear-gradient(to bottom, rgba(30, 30, 30, 0.8), rgba(24, 24, 24, 0.9)) !important;
  backdrop-filter: blur(10px) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  margin-top: auto !important;
}

/* Interaction buttons */
.post-card .group.p-2,
.post-card button.group {
  padding: 0.75rem !important;
  border-radius: 0.75rem !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  transition: all 0.2s ease !important;
  backdrop-filter: blur(5px) !important;
}

.post-card .group.p-2:hover,
.post-card button.group:hover {
  transform: translateY(-2px) scale(1.05) !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Platform badge */
.post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full {
  padding: 0.5rem 1rem !important;
  background-color: rgba(255, 255, 255, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.2s ease !important;
}

.post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full:hover {
  background-color: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.25) !important;
  transform: translateY(-1px) !important;
}

/* ===== RESPONSIVE CARD ADJUSTMENTS ===== */

/* Tablet adjustments */
@media (max-width: 1023px) {
  .post-card .notely-breathing-compact-md {
    padding: 1.25rem 1.25rem 0.75rem !important;
  }
  
  .post-card .post-content {
    padding: 0.75rem 1.25rem 1.25rem !important;
    font-size: 1rem !important;
  }
  
  .post-card .mt-3.pt-3.pb-2,
  .post-card .notely-post-footer > div {
    padding: 0.75rem 1.25rem !important;
  }
}

/* Mobile adjustments */
@media (max-width: 767px) {
  .post-card .notely-breathing-compact-md {
    padding: 1rem 1rem 0.5rem !important;
  }
  
  .post-card .post-content {
    padding: 0.5rem 1rem 1rem !important;
    font-size: 0.9375rem !important;
  }
  
  .post-card .mt-3.pt-3.pb-2,
  .post-card .notely-post-footer > div {
    padding: 0.5rem 1rem !important;
  }
  
  .post-card .w-8.h-8,
  .post-card .sm\\:w-10.sm\\:h-10 {
    width: 40px !important;
    height: 40px !important;
  }
  
  .post-card .author-name,
  .post-card .font-bold.text-lg {
    font-size: 1.125rem !important;
  }
  
  .post-card .group.p-2,
  .post-card button.group {
    padding: 0.5rem !important;
  }
}

/* Small mobile adjustments */
@media (max-width: 639px) {
  .post-card .notely-breathing-compact-md {
    padding: 0.75rem 0.75rem 0.25rem !important;
  }
  
  .post-card .post-content {
    padding: 0.25rem 0.75rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
  
  .post-card .mt-3.pt-3.pb-2,
  .post-card .notely-post-footer > div {
    padding: 0.25rem 0.75rem !important;
  }
  
  .post-card .w-8.h-8,
  .post-card .sm\\:w-10.sm\\:h-10 {
    width: 36px !important;
    height: 36px !important;
  }
  
  .post-card .author-name,
  .post-card .font-bold.text-lg {
    font-size: 1rem !important;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

@media (prefers-reduced-motion: reduce) {
  .post-card.notely-card[style],
  .post-card .group.p-2,
  .post-card button.group,
  .post-card .w-8.h-8,
  .post-card .sm\\:w-10.sm\\:h-10,
  .post-card .timestamp,
  .post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full {
    transition: none !important;
  }
  
  .post-card.notely-card[style]:hover,
  .post-card .group.p-2:hover,
  .post-card button.group:hover,
  .post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full:hover {
    transform: none !important;
  }
  
  .post-card:hover .w-8.h-8,
  .post-card:hover .sm\\:w-10.sm\\:h-10 {
    transform: none !important;
  }
}

/* ===== HIGH CONTRAST MODE ===== */

@media (forced-colors: active) {
  .post-card.notely-card[style] {
    border: 2px solid CanvasText !important;
    background: Canvas !important;
  }
  
  .post-card .author-name,
  .post-card .font-bold.text-lg {
    color: CanvasText !important;
    text-shadow: none !important;
  }
  
  .post-card .author-handle,
  .post-card .text-notely-text-muted,
  .post-card .timestamp {
    color: CanvasText !important;
  }
  
  .post-card .post-content {
    color: CanvasText !important;
  }
  
  .post-card .group.p-2,
  .post-card button.group,
  .post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full {
    border: 1px solid ButtonText !important;
    background: ButtonFace !important;
  }
  
  .post-card .mt-3.pt-3.pb-2,
  .post-card .notely-post-footer > div {
    background: Canvas !important;
    border-top: 1px solid CanvasText !important;
  }
}

/* ===== LOADING STATES ===== */

.posts-grid-loading {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)) !important;
  gap: var(--space-6) !important;
  width: 100% !important;
}

.post-card-skeleton {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.05) 25%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.05) 75%) !important;
  background-size: 200% 100% !important;
  animation: shimmer 2s infinite !important;
  border-radius: 1.25rem !important;
  height: 300px !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ===== EMPTY STATE ===== */

.posts-grid-empty {
  grid-column: 1 / -1 !important;
  text-align: center !important;
  padding: var(--space-12) var(--space-4) !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

.posts-grid-empty h3 {
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-semibold) !important;
  margin-bottom: var(--space-4) !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.posts-grid-empty p {
  font-size: var(--font-size-md) !important;
  line-height: var(--line-height-relaxed) !important;
  max-width: 500px !important;
  margin: 0 auto !important;
}