/* 
 * Dramatic Card Styles for Notely
 * Enhanced card designs with dramatic visual effects and improved interaction states
 */

/* ===== DRAMATIC CARD BASE ===== */

.dramatic-card {
  position: relative;
  background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-hover) 100%);
  border-radius: 1.25rem;
  border: 1px solid var(--color-border);
  overflow: hidden;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1), 
              box-shadow 0.4s cubic-bezier(0.34, 1.56, 0.64, 1), 
              border-color 0.3s ease;
  box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.2), 
              0 0 0 1px rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dramatic-card:hover {
  transform: translateY(-8px) scale(1.01);
  box-shadow: 0 20px 40px -5px rgba(0, 0, 0, 0.3), 
              0 0 0 1px rgba(255, 255, 255, 0.15);
  border-color: var(--color-primary-light);
}

.dramatic-card:active {
  transform: translateY(-4px) scale(0.99);
  box-shadow: 0 15px 25px -5px rgba(0, 0, 0, 0.2), 
              0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* ===== DRAMATIC CARD HEADER ===== */

.dramatic-card-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dramatic-card-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: var(--color-surface-hover);
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  flex-shrink: 0;
}

.dramatic-card:hover .dramatic-card-avatar {
  transform: scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.dramatic-card-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dramatic-card-header-content {
  flex-grow: 1;
  min-width: 0;
}

.dramatic-card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dramatic-card-subtitle {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dramatic-card-badge {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  background-color: var(--color-primary);
  border-radius: 9999px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dramatic-card:hover .dramatic-card-badge {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

/* ===== DRAMATIC CARD CONTENT ===== */

.dramatic-card-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.dramatic-card-text {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--color-text-primary);
  margin-bottom: 1.5rem;
}

.dramatic-card-media {
  margin: 0 -1.5rem;
  position: relative;
  overflow: hidden;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dramatic-card-media img {
  width: 100%;
  display: block;
  transition: transform 0.5s ease;
}

.dramatic-card:hover .dramatic-card-media img {
  transform: scale(1.03);
}

.dramatic-card-media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, 
                rgba(0, 0, 0, 0) 0%, 
                rgba(0, 0, 0, 0.3) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dramatic-card:hover .dramatic-card-media-overlay {
  opacity: 1;
}

.dramatic-card-media-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem 1.5rem;
  background: linear-gradient(to top, 
                rgba(0, 0, 0, 0.8) 0%, 
                rgba(0, 0, 0, 0) 100%);
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.dramatic-card:hover .dramatic-card-media-caption {
  transform: translateY(0);
}

.dramatic-card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: auto;
  padding-top: 1rem;
}

.dramatic-card-tag {
  padding: 0.25rem 0.75rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-text-secondary);
  transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease;
}

.dramatic-card-tag:hover {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-2px);
}

/* ===== DRAMATIC CARD FOOTER ===== */

.dramatic-card-footer {
  padding: 1.25rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(to bottom, 
              rgba(255, 255, 255, 0.03) 0%, 
              rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dramatic-card-metrics {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.dramatic-card-metric {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.dramatic-card-metric:hover {
  color: var(--color-text-primary);
}

.dramatic-card-metric-icon {
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dramatic-card-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dramatic-card-action {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease, 
              transform 0.3s ease, 
              box-shadow 0.3s ease, 
              color 0.3s ease;
  position: relative;
}

.dramatic-card-action:hover {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.dramatic-card-action:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dramatic-card-action-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 0.75rem;
  padding: 0.375rem 0.75rem;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.75rem;
  border-radius: 0.375rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;
}

.dramatic-card-action-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0.375rem;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
}

.dramatic-card-action:hover .dramatic-card-action-tooltip {
  opacity: 1;
  visibility: visible;
}

/* ===== DRAMATIC CARD VARIANTS ===== */

/* Quote Card */
.dramatic-card.quote-card {
  background: linear-gradient(135deg, 
              rgba(79, 70, 229, 0.1) 0%, 
              rgba(79, 70, 229, 0.05) 100%);
  border-color: rgba(79, 70, 229, 0.2);
}

.dramatic-card.quote-card .dramatic-card-content {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 3rem 2rem;
}

.dramatic-card.quote-card .dramatic-card-text {
  font-size: 1.25rem;
  font-style: italic;
  line-height: 1.7;
  color: var(--color-text-primary);
  position: relative;
  margin-bottom: 1.5rem;
}

.dramatic-card.quote-card .dramatic-card-text::before,
.dramatic-card.quote-card .dramatic-card-text::after {
  content: '"';
  font-size: 3rem;
  line-height: 0;
  color: var(--color-primary);
  opacity: 0.3;
  position: absolute;
}

.dramatic-card.quote-card .dramatic-card-text::before {
  top: 0.5rem;
  left: -1.5rem;
}

.dramatic-card.quote-card .dramatic-card-text::after {
  bottom: -0.5rem;
  right: -1.5rem;
  transform: rotate(180deg);
}

.dramatic-card.quote-card .dramatic-card-author {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-primary);
}

/* Featured Card */
.dramatic-card.featured-card {
  border-width: 0;
  background: linear-gradient(135deg, 
              var(--color-primary-dark) 0%, 
              var(--color-primary) 100%);
  box-shadow: 0 20px 40px -10px rgba(79, 70, 229, 0.5), 
              0 0 0 1px rgba(255, 255, 255, 0.1);
}

.dramatic-card.featured-card .dramatic-card-title,
.dramatic-card.featured-card .dramatic-card-text {
  color: white;
}

.dramatic-card.featured-card .dramatic-card-subtitle,
.dramatic-card.featured-card .dramatic-card-metric {
  color: rgba(255, 255, 255, 0.8);
}

.dramatic-card.featured-card .dramatic-card-tag {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.dramatic-card.featured-card .dramatic-card-action {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.dramatic-card.featured-card .dramatic-card-action:hover {
  background-color: white;
  color: var(--color-primary);
}

/* Platform-specific cards */
.dramatic-card.twitter-card {
  border-top: 6px solid var(--color-twitter);
}

.dramatic-card.linkedin-card {
  border-top: 6px solid var(--color-linkedin);
}

.dramatic-card.reddit-card {
  border-top: 6px solid var(--color-reddit);
}

.dramatic-card.instagram-card {
  border-top: 6px solid var(--color-instagram);
}

.dramatic-card.pinterest-card {
  border-top: 6px solid var(--color-pinterest);
}

.dramatic-card.web-card {
  border-top: 6px solid var(--color-web);
}

/* ===== DRAMATIC CARD ANIMATIONS ===== */

/* Hover lift effect */
.dramatic-card.hover-lift {
  transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), 
              box-shadow 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.dramatic-card.hover-lift:hover {
  transform: translateY(-16px);
  box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.4), 
              0 0 0 1px rgba(255, 255, 255, 0.15);
}

/* Hover glow effect */
.dramatic-card.hover-glow {
  transition: box-shadow 0.5s ease;
}

.dramatic-card.hover-glow:hover {
  box-shadow: 0 0 30px rgba(79, 70, 229, 0.5), 
              0 0 0 1px rgba(255, 255, 255, 0.15);
}

/* Hover reveal effect */
.dramatic-card.hover-reveal .dramatic-card-content {
  transform: translateY(30%);
  opacity: 0;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.dramatic-card.hover-reveal:hover .dramatic-card-content {
  transform: translateY(0);
  opacity: 1;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 1023px) {
  .dramatic-card-header {
    padding: 1.25rem;
  }
  
  .dramatic-card-content {
    padding: 1.25rem;
  }
  
  .dramatic-card-footer {
    padding: 1rem 1.25rem;
  }
  
  .dramatic-card-title {
    font-size: 1.125rem;
  }
  
  .dramatic-card-text {
    font-size: 0.9375rem;
  }
}

@media (max-width: 767px) {
  .dramatic-card-header {
    padding: 1rem;
  }
  
  .dramatic-card-content {
    padding: 1rem;
  }
  
  .dramatic-card-footer {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }
  
  .dramatic-card-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .dramatic-card-avatar {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .dramatic-card.quote-card .dramatic-card-content {
    padding: 2rem 1.5rem;
  }
  
  .dramatic-card.quote-card .dramatic-card-text {
    font-size: 1.125rem;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .dramatic-card,
  .dramatic-card-avatar,
  .dramatic-card-badge,
  .dramatic-card-media img,
  .dramatic-card-media-overlay,
  .dramatic-card-media-caption,
  .dramatic-card-tag,
  .dramatic-card-action,
  .dramatic-card-action-tooltip,
  .dramatic-card.hover-lift,
  .dramatic-card.hover-glow,
  .dramatic-card.hover-reveal .dramatic-card-content {
    transition: none;
  }
  
  .dramatic-card:hover,
  .dramatic-card:hover .dramatic-card-avatar,
  .dramatic-card:hover .dramatic-card-badge,
  .dramatic-card:hover .dramatic-card-media img,
  .dramatic-card:hover .dramatic-card-media-caption,
  .dramatic-card.hover-lift:hover,
  .dramatic-card.hover-reveal:hover .dramatic-card-content {
    transform: none;
  }
}

/* High contrast mode */
@media (forced-colors: active) {
  .dramatic-card {
    border: 2px solid ButtonText;
    background: Canvas;
  }
  
  .dramatic-card:hover {
    border-color: Highlight;
  }
  
  .dramatic-card-header,
  .dramatic-card-footer {
    border-color: ButtonText;
    background: Canvas;
  }
  
  .dramatic-card-title,
  .dramatic-card-text {
    color: CanvasText;
  }
  
  .dramatic-card-subtitle,
  .dramatic-card-metric {
    color: CanvasText;
  }
  
  .dramatic-card-tag {
    border: 1px solid ButtonText;
    background: Canvas;
    color: CanvasText;
  }
  
  .dramatic-card-tag:hover {
    border-color: Highlight;
    color: Highlight;
  }
  
  .dramatic-card-action {
    border: 1px solid ButtonText;
    background: Canvas;
    color: CanvasText;
  }
  
  .dramatic-card-action:hover {
    border-color: Highlight;
    color: Highlight;
  }
  
  .dramatic-card.featured-card {
    border: 2px solid Highlight;
  }
  
  .dramatic-card.twitter-card,
  .dramatic-card.linkedin-card,
  .dramatic-card.reddit-card,
  .dramatic-card.instagram-card,
  .dramatic-card.pinterest-card,
  .dramatic-card.web-card {
    border-top: 2px solid Highlight;
  }
}