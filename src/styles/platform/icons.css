/* 
 * Platform Icons - Optimized
 * Consolidated platform-specific icon styles and colors
 */

/* ===== PLATFORM ICON BASE ===== */
.platform-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
  transition: all var(--duration-200) var(--ease-out);
}

.platform-icon-sm {
  width: 16px;
  height: 16px;
}

.platform-icon-lg {
  width: 24px;
  height: 24px;
}

.platform-icon-xl {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
}

/* ===== PLATFORM COLORS ===== */
.platform-twitter {
  background-color: var(--color-twitter);
  color: white;
}

.platform-linkedin {
  background-color: var(--color-linkedin);
  color: white;
}

.platform-reddit {
  background-color: var(--color-reddit);
  color: white;
}

.platform-instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
}

.platform-pinterest {
  background-color: var(--color-pinterest);
  color: white;
}

.platform-web {
  background-color: var(--color-web);
  color: white;
}

/* ===== PLATFORM BADGES ===== */
.platform-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.platform-badge-twitter {
  background-color: rgba(26, 140, 216, 0.1);
  border: var(--border-width-1) solid rgba(26, 140, 216, 0.3);
  color: var(--color-twitter);
}

.platform-badge-linkedin {
  background-color: rgba(10, 102, 194, 0.1);
  border: var(--border-width-1) solid rgba(10, 102, 194, 0.3);
  color: var(--color-linkedin);
}

.platform-badge-reddit {
  background-color: rgba(255, 69, 0, 0.1);
  border: var(--border-width-1) solid rgba(255, 69, 0, 0.3);
  color: var(--color-reddit);
}

.platform-badge-instagram {
  background-color: rgba(193, 53, 132, 0.1);
  border: var(--border-width-1) solid rgba(193, 53, 132, 0.3);
  color: var(--color-instagram);
}

.platform-badge-pinterest {
  background-color: rgba(230, 0, 35, 0.1);
  border: var(--border-width-1) solid rgba(230, 0, 35, 0.3);
  color: var(--color-pinterest);
}

.platform-badge-web {
  background-color: rgba(16, 185, 129, 0.1);
  border: var(--border-width-1) solid rgba(16, 185, 129, 0.3);
  color: var(--color-web);
}

/* ===== PLATFORM INDICATORS ===== */
.platform-indicator {
  position: relative;
}

.platform-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
}

.platform-indicator.twitter::before {
  background-color: var(--color-twitter);
}

.platform-indicator.linkedin::before {
  background-color: var(--color-linkedin);
}

.platform-indicator.reddit::before {
  background-color: var(--color-reddit);
}

.platform-indicator.instagram::before {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.platform-indicator.pinterest::before {
  background-color: var(--color-pinterest);
}

.platform-indicator.web::before {
  background-color: var(--color-web);
}

/* ===== PLATFORM LOGOS (SVG Icons) ===== */
.platform-logo {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.platform-logo-sm {
  width: 16px;
  height: 16px;
}

.platform-logo-lg {
  width: 24px;
  height: 24px;
}

.platform-logo-xl {
  width: 32px;
  height: 32px;
}

/* Twitter/X Logo */
.platform-logo.twitter {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z'/%3E%3C/svg%3E");
}

/* LinkedIn Logo */
.platform-logo.linkedin {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230077b5'%3E%3Cpath d='M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'/%3E%3C/svg%3E");
}

/* Reddit Logo */
.platform-logo.reddit {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff4500'%3E%3Cpath d='M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z'/%3E%3C/svg%3E");
}

/* Instagram Logo */
.platform-logo.instagram {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='url(%23instagram-gradient)'%3E%3Cdefs%3E%3ClinearGradient id='instagram-gradient' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23f09433'/%3E%3Cstop offset='25%25' style='stop-color:%23e6683c'/%3E%3Cstop offset='50%25' style='stop-color:%23dc2743'/%3E%3Cstop offset='75%25' style='stop-color:%23cc2366'/%3E%3Cstop offset='100%25' style='stop-color:%23bc1888'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath d='M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z'/%3E%3C/svg%3E");
}

/* Pinterest Logo */
.platform-logo.pinterest {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23e60023'%3E%3Cpath d='M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-12C24.007 5.367 18.641.001.012.001z'/%3E%3C/svg%3E");
}

/* Web/Generic Logo */
.platform-logo.web {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2310b981'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z'/%3E%3C/svg%3E");
}

/* ===== PLATFORM HOVER EFFECTS ===== */
.platform-icon:hover,
.platform-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.platform-logo:hover {
  transform: scale(1.1);
}

/* ===== PLATFORM COMBINATIONS ===== */
.platform-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.platform-card-header .platform-icon {
  margin-right: var(--space-2);
}

.platform-nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: all var(--duration-200) var(--ease-out);
}

.platform-nav-item:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-text-primary);
}

.platform-nav-item.active {
  background-color: var(--color-primary);
  color: white;
}

.platform-nav-item .platform-icon {
  opacity: 0.8;
}

.platform-nav-item:hover .platform-icon,
.platform-nav-item.active .platform-icon {
  opacity: 1;
}

/* ===== ACCESSIBILITY ===== */
.platform-icon[aria-label],
.platform-logo[aria-label] {
  cursor: help;
}

/* Screen reader text for platform icons */
.platform-sr-text {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== HIGH CONTRAST MODE ===== */
@media (forced-colors: active) {
  .platform-icon,
  .platform-badge,
  .platform-logo {
    border: 1px solid ButtonText;
    background-color: ButtonFace;
    color: ButtonText;
  }
  
  .platform-indicator::before {
    background-color: Highlight;
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  .platform-icon,
  .platform-badge,
  .platform-logo,
  .platform-nav-item {
    transition: none;
  }
  
  .platform-icon:hover,
  .platform-badge:hover,
  .platform-logo:hover {
    transform: none;
  }
}