/**
 * Theme Manager for Notely
 * 
 * This script enhances the existing card components by adding data attributes
 * and special classes to improve their visual appearance.
 */

// Function to enhance all cards on the page
function enhanceCards() {
  // Get all post cards
  const cards = document.querySelectorAll('.post-card');
  
  cards.forEach(card => {
    // Add data-platform attribute based on content
    const platformBadge = card.querySelector('[title="X/Twitter"]');
    if (platformBadge) {
      card.setAttribute('data-platform', 'twitter');
    }
    
    // Check for LinkedIn content
    if (card.innerHTML.includes('LinkedIn') || card.innerHTML.includes('linkedin')) {
      card.setAttribute('data-platform', 'linkedin');
    }
    
    // Check for Reddit content
    if (card.innerHTML.includes('Reddit') || card.innerHTML.includes('reddit')) {
      card.setAttribute('data-platform', 'reddit');
    }
    
    // Check for Instagram content
    if (card.innerHTML.includes('Instagram') || card.innerHTML.includes('instagram')) {
      card.setAttribute('data-platform', 'instagram');
    }
    
    // Add special classes for specific card types
    if (card.innerHTML.includes('Daily Wisdom') || card.innerHTML.includes('daily wisdom')) {
      card.setAttribute('data-post-id', 'daily-wisdom');
    }
    
    // Check if it's a quote card
    if (card.innerHTML.includes('In this morning of scattered fragments')) {
      card.setAttribute('data-post-id', 'wisdom');
    }
    
    // Check if it's a featured content
    if (card.innerHTML.includes('AI Office Suite') || card.innerHTML.includes('context.ai')) {
      card.setAttribute('data-post-id', 'ai-office');
    }
  });
}

// Run the enhancement when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', enhanceCards);

// Also run when the page content might have changed (for dynamic content)
const observer = new MutationObserver(mutations => {
  mutations.forEach(mutation => {
    if (mutation.addedNodes.length > 0) {
      setTimeout(enhanceCards, 100); // Small delay to ensure DOM is updated
    }
  });
});

// Start observing the document body for changes
observer.observe(document.body, { childList: true, subtree: true });

// Export the function for direct use in components
export default enhanceCards;