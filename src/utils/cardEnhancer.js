/**
 * Card Enhancer Utility
 * 
 * This script enhances the existing card components by adding special classes
 * and attributes to improve their visual appearance.
 */

// Function to enhance all cards on the page
function enhanceCards() {
  // Get all post cards
  const cards = document.querySelectorAll('.post-card');
  
  cards.forEach(card => {
    // Add data-platform attribute based on content
    const platformBadge = card.querySelector('.flex.items-center.space-x-2.px-2.py-1.rounded-full');
    if (platformBadge) {
      const platformText = platformBadge.textContent.toLowerCase();
      
      if (platformText.includes('twitter') || platformText.includes('x/twitter') || platformText.includes('x')) {
        card.setAttribute('data-platform', 'twitter');
      } else if (platformText.includes('linkedin')) {
        card.setAttribute('data-platform', 'linkedin');
      } else if (platformText.includes('reddit')) {
        card.setAttribute('data-platform', 'reddit');
      } else if (platformText.includes('instagram')) {
        card.setAttribute('data-platform', 'instagram');
      } else if (platformText.includes('pinterest')) {
        card.setAttribute('data-platform', 'pinterest');
      } else if (platformText.includes('web')) {
        card.setAttribute('data-platform', 'web');
      }
    }
    
    // Add special classes for specific card types
    const cardTitle = card.querySelector('.author-name');
    const cardContent = card.querySelector('.post-content');
    
    if (cardTitle && cardTitle.textContent.includes('Daily Wisdom')) {
      card.classList.add('daily-wisdom');
    }
    
    if (cardContent) {
      // Check if it's a quote card
      if (
        cardContent.textContent.includes('"') || 
        cardContent.textContent.includes('"') ||
        cardContent.textContent.includes('quote') ||
        cardContent.textContent.includes('Quote')
      ) {
        card.classList.add('quote-card');
      }
      
      // Check if it's a featured content
      if (
        cardContent.textContent.includes('AI Office Suite') ||
        cardContent.textContent.includes('featured') ||
        cardContent.textContent.includes('Featured')
      ) {
        card.classList.add('featured-card');
      }
    }
    
    // Add hover effects to media containers
    const mediaContainers = card.querySelectorAll('img:not(.rounded-full)');
    mediaContainers.forEach(img => {
      const parent = img.parentElement;
      if (parent && !parent.classList.contains('card-media-container')) {
        parent.classList.add('card-media-container');
        img.classList.add('card-media');
      }
    });
  });
}

// Run the enhancement when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', enhanceCards);

// Also run when the page content might have changed (for dynamic content)
const observer = new MutationObserver(mutations => {
  mutations.forEach(mutation => {
    if (mutation.addedNodes.length > 0) {
      enhanceCards();
    }
  });
});

// Start observing the document body for changes
observer.observe(document.body, { childList: true, subtree: true });

// Export the function for direct use in components
export default enhanceCards;