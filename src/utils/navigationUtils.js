/**
 * Navigation Utilities
 * Helper functions for navigation components like platform tabs and filters
 */

/**
 * Initialize platform tabs with proper interaction and accessibility
 * @param {string} containerSelector - The selector for the tabs container
 */
export function initPlatformTabs(containerSelector = '.platform-tabs-container') {
  const tabsContainer = document.querySelector(containerSelector);
  if (!tabsContainer) return;
  
  const tabs = tabsContainer.querySelectorAll('.platform-tab');
  
  // Check if container has overflow and add appropriate class
  const checkOverflow = () => {
    if (tabsContainer.scrollWidth > tabsContainer.clientWidth) {
      tabsContainer.classList.add('has-overflow');
    } else {
      tabsContainer.classList.remove('has-overflow');
    }
  };
  
  // Initial overflow check
  checkOverflow();
  
  // Listen for window resize to recheck overflow
  window.addEventListener('resize', checkOverflow);
  
  // Add keyboard navigation
  tabs.forEach((tab, index) => {
    tab.setAttribute('role', 'tab');
    tab.setAttribute('tabindex', '0');
    tab.setAttribute('aria-selected', tab.classList.contains('active') ? 'true' : 'false');
    
    // Handle keyboard navigation
    tab.addEventListener('keydown', (e) => {
      let targetTab = null;
      
      switch (e.key) {
        case 'ArrowRight':
          targetTab = index < tabs.length - 1 ? tabs[index + 1] : tabs[0];
          break;
        case 'ArrowLeft':
          targetTab = index > 0 ? tabs[index - 1] : tabs[tabs.length - 1];
          break;
        case ' ':
        case 'Enter':
          tab.click();
          e.preventDefault();
          break;
      }
      
      if (targetTab) {
        targetTab.focus();
        e.preventDefault();
      }
    });
    
    // Add ripple effect on click
    tab.addEventListener('click', (e) => {
      if (window.matchMedia('(prefers-reduced-motion: no-preference)').matches) {
        const rect = tab.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const ripple = document.createElement('span');
        ripple.classList.add('ripple');
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;
        
        tab.appendChild(ripple);
        
        setTimeout(() => {
          ripple.remove();
        }, 600);
      }
    });
  });
}

/**
 * Initialize filter chips with proper interaction and accessibility
 * @param {string} containerSelector - The selector for the filter container
 */
export function initFilterChips(containerSelector = '.filter-container') {
  const filterContainer = document.querySelector(containerSelector);
  if (!filterContainer) return;
  
  const filterChips = filterContainer.querySelectorAll('.filter-chip');
  
  filterChips.forEach(chip => {
    chip.setAttribute('role', 'checkbox');
    chip.setAttribute('aria-checked', chip.classList.contains('active') ? 'true' : 'false');
    chip.setAttribute('tabindex', '0');
    
    // Toggle active state on click
    chip.addEventListener('click', () => {
      const isActive = chip.classList.toggle('active');
      chip.setAttribute('aria-checked', isActive ? 'true' : 'false');
      
      // Dispatch custom event for filter change
      const event = new CustomEvent('filter:change', {
        bubbles: true,
        detail: {
          filter: chip.dataset.filter,
          value: chip.dataset.value,
          active: isActive
        }
      });
      chip.dispatchEvent(event);
    });
    
    // Handle keyboard interaction
    chip.addEventListener('keydown', (e) => {
      if (e.key === ' ' || e.key === 'Enter') {
        e.preventDefault();
        chip.click();
      }
    });
    
    // Handle remove button if present
    const removeButton = chip.querySelector('.filter-chip-remove');
    if (removeButton) {
      removeButton.setAttribute('aria-label', 'Remove filter');
      removeButton.setAttribute('role', 'button');
      removeButton.setAttribute('tabindex', '0');
      
      removeButton.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent triggering the chip's click event
        
        // Dispatch custom event for filter removal
        const event = new CustomEvent('filter:remove', {
          bubbles: true,
          detail: {
            filter: chip.dataset.filter,
            value: chip.dataset.value
          }
        });
        chip.dispatchEvent(event);
        
        // Optionally remove the chip from DOM
        chip.remove();
      });
      
      removeButton.addEventListener('keydown', (e) => {
        if (e.key === ' ' || e.key === 'Enter') {
          e.preventDefault();
          removeButton.click();
        }
      });
    }
  });
  
  // Handle clear all button if present
  const clearAllButton = document.querySelector('.filter-clear-all');
  if (clearAllButton) {
    clearAllButton.addEventListener('click', () => {
      filterChips.forEach(chip => {
        if (chip.classList.contains('active')) {
          chip.classList.remove('active');
          chip.setAttribute('aria-checked', 'false');
        }
      });
      
      // Dispatch custom event for clearing all filters
      const event = new CustomEvent('filter:clear-all', {
        bubbles: true
      });
      clearAllButton.dispatchEvent(event);
    });
  }
}

/**
 * Create a new filter chip element
 * @param {string} label - The label text for the filter
 * @param {string} filter - The filter type
 * @param {string} value - The filter value
 * @param {boolean} active - Whether the filter is active
 * @param {boolean} removable - Whether the filter can be removed
 * @returns {HTMLElement} The created filter chip element
 */
export function createFilterChip(label, filter, value, active = false, removable = true) {
  const chip = document.createElement('div');
  chip.classList.add('filter-chip');
  if (active) chip.classList.add('active');
  chip.setAttribute('role', 'checkbox');
  chip.setAttribute('aria-checked', active ? 'true' : 'false');
  chip.setAttribute('tabindex', '0');
  chip.dataset.filter = filter;
  chip.dataset.value = value;
  
  const labelSpan = document.createElement('span');
  labelSpan.textContent = label;
  chip.appendChild(labelSpan);
  
  if (removable) {
    const removeButton = document.createElement('button');
    removeButton.classList.add('filter-chip-remove');
    removeButton.setAttribute('aria-label', `Remove ${label} filter`);
    removeButton.setAttribute('type', 'button');
    removeButton.innerHTML = '<svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
    chip.appendChild(removeButton);
    
    removeButton.addEventListener('click', (e) => {
      e.stopPropagation();
      
      const event = new CustomEvent('filter:remove', {
        bubbles: true,
        detail: {
          filter,
          value
        }
      });
      chip.dispatchEvent(event);
      
      chip.remove();
    });
  }
  
  // Toggle active state on click
  chip.addEventListener('click', () => {
    const isActive = chip.classList.toggle('active');
    chip.setAttribute('aria-checked', isActive ? 'true' : 'false');
    
    const event = new CustomEvent('filter:change', {
      bubbles: true,
      detail: {
        filter,
        value,
        active: isActive
      }
    });
    chip.dispatchEvent(event);
  });
  
  // Handle keyboard interaction
  chip.addEventListener('keydown', (e) => {
    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault();
      chip.click();
    }
  });
  
  return chip;
}

/**
 * Create a new tag element
 * @param {string} label - The label text for the tag
 * @param {string} color - Optional color class for the tag
 * @param {number} count - Optional count to display
 * @returns {HTMLElement} The created tag element
 */
export function createTag(label, color = '', count = null) {
  const tag = document.createElement('div');
  tag.classList.add('tag');
  if (color) tag.classList.add(color);
  tag.setAttribute('tabindex', '0');
  
  const labelSpan = document.createElement('span');
  labelSpan.textContent = label;
  tag.appendChild(labelSpan);
  
  if (count !== null) {
    const countSpan = document.createElement('span');
    countSpan.classList.add('tag-count');
    countSpan.textContent = count;
    tag.appendChild(countSpan);
  }
  
  // Toggle active state on click
  tag.addEventListener('click', () => {
    tag.classList.toggle('active');
    
    const event = new CustomEvent('tag:click', {
      bubbles: true,
      detail: {
        label,
        active: tag.classList.contains('active')
      }
    });
    tag.dispatchEvent(event);
  });
  
  // Handle keyboard interaction
  tag.addEventListener('keydown', (e) => {
    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault();
      tag.click();
    }
  });
  
  return tag;
}