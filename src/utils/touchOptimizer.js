/**
 * Touch Optimizer for Notely
 * Enhances touch interactions on mobile devices
 */

/**
 * Applies touch optimizations to interactive elements
 */
export function applyTouchOptimizations() {
  // Check if we're on a touch device
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  if (!isTouchDevice) return;
  
  // Add a class to the body to indicate touch device
  document.body.classList.add('touch-device');
  
  // Find all interactive elements
  const interactiveElements = document.querySelectorAll(
    'button, a, .platform-tab, .filter-chip, .tag, [role="button"]'
  );
  
  // Apply touch-friendly styles
  interactiveElements.forEach(element => {
    // Add touch feedback
    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });
    element.addEventListener('touchcancel', handleTouchEnd, { passive: true });
    
    // Ensure minimum touch target size
    ensureMinimumTouchTargetSize(element);
  });
}

/**
 * Handle touch start event
 */
function handleTouchStart(event) {
  this.classList.add('touch-active');
}

/**
 * Handle touch end event
 */
function handleTouchEnd(event) {
  this.classList.remove('touch-active');
}

/**
 * Ensure element meets minimum touch target size requirements
 */
function ensureMinimumTouchTargetSize(element) {
  const MIN_SIZE = 44; // Minimum size in pixels for touch targets (WCAG recommendation)
  
  const rect = element.getBoundingClientRect();
  const width = rect.width;
  const height = rect.height;
  
  // If element is smaller than minimum size, add padding or adjust size
  if (width < MIN_SIZE || height < MIN_SIZE) {
    element.classList.add('touch-target-enhanced');
    
    // Calculate required padding
    const paddingX = Math.max(0, (MIN_SIZE - width) / 2);
    const paddingY = Math.max(0, (MIN_SIZE - height) / 2);
    
    // Apply padding if element is not already positioned
    const computedStyle = window.getComputedStyle(element);
    if (computedStyle.position === 'static') {
      element.style.position = 'relative';
    }
    
    // Add padding to increase touch target size
    if (paddingX > 0) {
      element.style.paddingLeft = `${paddingX}px`;
      element.style.paddingRight = `${paddingX}px`;
    }
    
    if (paddingY > 0) {
      element.style.paddingTop = `${paddingY}px`;
      element.style.paddingBottom = `${paddingY}px`;
    }
  }
}

/**
 * Initialize touch optimizations
 */
export function initTouchOptimizations() {
  // Apply optimizations when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyTouchOptimizations);
  } else {
    applyTouchOptimizations();
  }
  
  // Reapply optimizations when content changes
  const observer = new MutationObserver(mutations => {
    applyTouchOptimizations();
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// Add CSS for touch feedback
export function addTouchStyles() {
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    .touch-device .touch-active {
      opacity: 0.7;
      transform: scale(0.97);
      transition: opacity 0.1s ease-out, transform 0.1s ease-out;
    }
    
    .touch-device .touch-target-enhanced {
      position: relative;
    }
    
    .touch-device .touch-target-enhanced::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 44px;
      height: 44px;
      transform: translate(-50%, -50%);
      z-index: -1;
    }
    
    @media (prefers-reduced-motion: reduce) {
      .touch-device .touch-active {
        transform: none;
        transition: opacity 0.1s ease-out;
      }
    }
  `;
  
  document.head.appendChild(styleElement);
}

// Export default function to initialize everything
export default function initializeTouchOptimizations() {
  addTouchStyles();
  initTouchOptimizations();
}