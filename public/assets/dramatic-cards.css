/* 
 * Dramatic Card Styles for Notely
 * Direct CSS injection for immediate effect
 */

/* Card Base Improvements */
.post-card {
  border-radius: 1rem !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease !important;
  border-width: 1px !important;
  border-top-width: 5px !important;
  background: linear-gradient(to bottom, #1e1e1e, #181818) !important;
  overflow: hidden !important;
}

/* Card hover state */
.post-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.35) !important;
  border-color: rgba(99, 102, 241, 0.7) !important;
}

/* Platform-specific top borders */
.post-card div[title="X/Twitter"] {
  border-top-color: #1da1f2 !important;
}

/* Card Header Improvements */
.post-card > div:first-child {
  padding: 1.5rem 1.5rem 0.75rem !important;
}

/* Avatar Improvements */
.post-card .w-8.h-8,
.post-card .sm\:w-10.sm\:h-10,
.post-card .rounded-full.bg-gray-200 {
  width: 48px !important;
  height: 48px !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  transition: transform 0.3s ease !important;
}

.post-card:hover .w-8.h-8,
.post-card:hover .sm\:w-10.sm\:h-10,
.post-card:hover .rounded-full.bg-gray-200 {
  transform: scale(1.05) !important;
}

/* Author Name Improvements */
.post-card .author-name,
.post-card .font-bold.text-lg {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  letter-spacing: -0.01em !important;
  color: rgba(255, 255, 255, 0.95) !important;
  margin-bottom: 0.25rem !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Author Handle & Timestamp Improvements */
.post-card .author-handle,
.post-card .text-notely-text-muted.notely-body {
  font-size: 0.875rem !important;
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 500 !important;
}

.post-card .timestamp,
.post-card .text-notely-text-muted.hover\:text-notely-text-secondary {
  font-size: 0.875rem !important;
  color: rgba(255, 255, 255, 0.6) !important;
  transition: color 0.2s ease !important;
}

.post-card .timestamp:hover,
.post-card .text-notely-text-muted.hover\:text-notely-text-secondary:hover {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Post Content Improvements */
.post-card .post-content,
.post-card .font-normal.text-\[15px\] {
  padding: 1rem 1.5rem 1.5rem !important;
  font-size: 1.0625rem !important;
  line-height: 1.6 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  letter-spacing: -0.01em !important;
}

/* Card Footer Improvements */
.post-card .mt-3.pt-3.pb-2,
.post-card .notely-post-footer > div {
  padding: 1rem 1.5rem !important;
  background: linear-gradient(to bottom, rgba(30, 30, 30, 0.5), rgba(24, 24, 24, 0.8)) !important;
  backdrop-filter: blur(5px) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Interaction Buttons Improvements */
.post-card .group.p-2,
.post-card button.group {
  padding: 0.625rem !important;
  border-radius: 0.5rem !important;
  transition: all 0.2s ease !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.post-card .group.p-2:hover,
.post-card button.group:hover {
  transform: translateY(-2px) scale(1.05) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Metadata Items Improvements */
.post-card .flex.items-center.space-x-4 > div,
.post-card .group.flex.items-center {
  padding: 0.375rem 0.75rem !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;
  background-color: rgba(255, 255, 255, 0.03) !important;
}

.post-card .flex.items-center.space-x-4 > div:hover,
.post-card .group.flex.items-center:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Platform Badge Improvements */
.post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full,
.post-card div[title="X/Twitter"] {
  padding: 0.375rem 0.875rem !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  transition: all 0.2s ease !important;
  border-radius: 9999px !important;
}

.post-card .flex.items-center.space-x-2.px-2.py-1.rounded-full:hover,
.post-card div[title="X/Twitter"]:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Apply Twitter-specific styling */
.post-card[data-post-id="1938833179957154085"] {
  border-top-color: #1da1f2 !important;
}

/* Force override for specific card elements */
.post-card.notely-card.relative.bg-notely-card {
  background: linear-gradient(to bottom, #1e1e1e, #181818) !important;
  border-radius: 1rem !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
}

.post-card.notely-card.relative.bg-notely-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.35) !important;
}

/* Force override for border colors */
.post-card.notely-card.border.border-\[#2F2F2F\] {
  border-color: rgba(47, 47, 47, 1) !important;
  border-top-width: 5px !important;
}

.post-card.notely-card.border.border-\[#2F2F2F\]:hover {
  border-color: rgba(99, 102, 241, 0.7) !important;
}

/* Force override for Twitter cards */
.post-card.notely-card div[title="X/Twitter"] + span.text-xs {
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 500 !important;
}